{"name": "cym702_pet", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode dev", "live": "vue-cli-service serve --mode live", "local": "vue-cli-service serve --mode local", "dev": "vue-cli-service serve --mode dev", "lint": "vue-cli-service lint", "i18n:report": "vue-cli-service i18n:report --src \"./src/**/*.?(js|vue)\" --locales \"./src/locales/**/*.json\"", "deploy-dev": "aws s3 sync ./production s3://cym-pet-webview-dev.yellosis.com --profile=cym-front", "deploy": "aws s3 sync ./production s3://cym-pet-webview --profile=cym-front", "invalidate-dev": "aws cloudfront create-invalidation --profile=cym-front --distribution-id E3DZG2S6LL2ZP6 --paths '/*'", "invalidate": "aws cloudfront create-invalidation --profile=cym-front --distribution-id E30EUVZ6UH518J --paths '/*'"}, "dependencies": {"apexcharts": "^3.35.3", "base-64": "^1.0.0", "core-js": "^3.6.5", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "sha256": "^0.2.0", "vue": "^2.6.11", "vue-apexcharts": "^1.6.2", "vue-apple-signin": "^0.1.2", "vue-circular-count-down-timer": "^1.0.4", "vue-country-dropdown": "^2.0.8", "vue-fixed-header": "^3.2.15", "vue-highlight.js": "^3.1.0", "vue-i18n": "^8.26.3", "vue-router": "^3.2.0", "vue-scroll-picker": "^0.8.0", "vue-spinner": "^1.0.4", "vue-switches": "^2.0.1", "vuetify": "^2.4.0", "vuex": "^3.6.2", "vuex-persist": "^3.1.3", "vuex-persistedstate": "^4.1.0", "yarn": "^1.22.21"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "^4.5.13", "@vue/cli-service": "~4.5.0", "axios": "^0.21.4", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "sass": "~1.32.0", "sass-loader": "^10.0.0", "vue-cli-plugin-axios": "0.0.4", "vue-cli-plugin-i18n": "~2.3.1", "vue-cli-plugin-vuetify": "~2.4.1", "vue-svg-loader": "^0.16.0", "vue-template-compiler": "^2.6.14", "vuetify-loader": "^1.7.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-unused-vars": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "packageManager": "yarn@4.1.0"}