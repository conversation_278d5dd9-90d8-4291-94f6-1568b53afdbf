<template>
  <v-app :style="fontStyles">
    <v-main>
      <router-view />
    </v-main>
  </v-app>
</template>

<script>
export default {
  name: "App",
  computed: {
    fontStyles() {
      return {
        "font-family": this.fontFamily,
        "letter-spacing": this.letterSpacing,
      };
    },

    fontFamily() {
      return this.$i18n.locale.includes("ko") ? "Noto Sans KR" : "GilroyMedium";
    },

    letterSpacing() {
      return this.$i18n.locale.includes("ko") ? "-0.03em" : "0em";
    },
  },
};
</script>

<style lang="scss">
@import "@/styles/global.scss";

body,
html {
  font-size: 18px;
  // letter-spacing: -0.03em;
  // overscroll-behavior-y: none;
}

img,
a {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

b,
strong {
  font-weight: 700 !important;
}

sub,
sup {
  font-size: 50% !important;
  top: -0.7em !important;
}
#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #000000;
  width: 100% !important;
  height: 100%;
  max-width: 450px !important;
  margin: auto;
}

@media screen and (min-width: 789px) {
  #app {
    display: none !important;
  }
}
</style>
