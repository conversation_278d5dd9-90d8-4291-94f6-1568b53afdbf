const userAgent = navigator.userAgent.toLowerCase();
// console.log(userAgent)

// Web -> App Bridge
export default {
  requestCamera(message) {
    // console.log(JSON.stringify({ action: "REQ-TAKE-SHEET", payload: message }));
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-TAKE-SHEET", payload: message })
    );
  },

  openUrl(message) {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-OPEN-URL", payload: message })
    );
  },

  cacheClear(message = {}) {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-CACHE-CLEAR", payload: message })
    );
  },

  loginSuccess(message = {}) {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-LOGIN-SUCCESS", payload: message })
    );
  },

  kakaoLogin() {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-KAKAO-LOGIN" })
    );
  },

  googleLogin() {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-GOOGLE-LOGIN" })
    );
  },

  googleLogout() {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-GOOGLE-LOGOUT" })
    );
  },

  appleLogin() {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-APPLE-LOGIN" })
    );
  },
  getDeviceId() {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-DEVICE-ID" })
    );
  },

  // Exam Alert Functions
  getWeeklyAlerts() {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-LIST-LN-WEEKLY" })
    );
  },
  getMonthlyAlerts() {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-LIST-LN-MONTHLY" })
    );
  },
  createtWeeklyAlerts(message) {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-CREATE-LN-WEEKLY", payload: message })
    );
  },
  deleteWeeklyAlerts(message) {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-DELETE-LN-WEEKLY", payload: message })
    );
  },
  createtMonthlyAlerts(message) {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-CREATE-LN-MONTHLY", payload: message })
    );
  },
  deleteMonthlyAlerts(message) {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-DELETE-LN-MONTHLY", payload: message })
    );
  },

  // Alert Settings Functions
  getFcmAuthority() {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-IS-AUTHORIZED-FCM" })
    );
  },
  requestPermissionFcm() {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-PREMISSION-FCM" })
    );
  },
  registerFcm() {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-REGISTER-DEVICE-FCM" })
    );
  },
  unRegisterFcm() {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-UNREGISTER-DEVICE-FCM" })
    );
  },
  subscribeTopic(message) {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-SUBSCRIBE-TOPIC-FCM", payload: message })
    );
  },
  unsubscribeTopic(message) {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({ action: "REQ-UNSUBSCRIBE-TOPIC-FCM", payload: message })
    );
  },
  // getVersion(message) {
  //   if (userAgent.indexOf("android") !== -1) {
  //     window.Android.getVersion();
  //   } else if (userAgent.indexOf("iphone") !== -1 || userAgent.indexOf("ipad") !== -1) {
  //     window.webkit.messageHandlers.webViewScriptHandler.postMessage(message);
  //   }
  // },
};
