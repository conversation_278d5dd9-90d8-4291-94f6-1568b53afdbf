import i18n from "../i18n";

export const explainations = {
  blood: i18n.t("blood_info"),
  glucose: i18n.t("glucose_info"),
  protein: i18n.t("protein_info"),
  ph: i18n.t("ph_info"),
  ketone: i18n.t("ketone_info"),
};

export const bloodDetailInfo = {
  normal: i18n.t("blood_good_level_modal"),
  warning: i18n.t("blood_warning_level_modal"),
  caution: i18n.t("blood_caution_level_modal"),
  danger: i18n.t("blood_danger_level_modal"),
};

export const glucoseDetailInfo = {
  normal: i18n.t("glucose_good_level_modal"),
  warning: i18n.t("glucose_warning_level_modal"),
  caution: i18n.t("glucose_caution_level_modal"),
  danger: i18n.t("glucose_danger_level_modal"),
};

export const proteinDetailInfo = {
  normal: i18n.t("protein_good_level_modal"),
  warning: i18n.t("protein_warning_level_modal"),
  caution: i18n.t("protein_caution_level_modal"),
  danger: i18n.t("protein_danger_level_modal"),
};

export const ketoneDetailInfo = {
  normal: i18n.t("normal"),
  caution_plus_minus: i18n.t("ketone_caution_level_plus_minus"),
  caution_plus: i18n.t("ketone_caution_level_plus"),
  warning: i18n.t("caution"),
  danger: i18n.t("danger"),
};

export const phDetailInfo = {
  normal: i18n.t("ph_good_level_modal"),
  warning: i18n.t("ph_warning_level_modal"),
};

export const bloodTxt = {
  normal: i18n.t("blood_good_level"),
  warning: i18n.t("blood_warning_level"),
  caution: i18n.t("blood_caution_level"),
  danger: i18n.t("blood_danger_level"),
};

export const glucoseTxt = {
  goodMinus: i18n.t("glucose_good_minus_level"),
  goodPlusMinus: i18n.t("glucose_good_plus_minus_level"),
  warning: i18n.t("glucose_warning_level"),
  caution: i18n.t("glucose_caution_level"),
  danger: i18n.t("glucose_danger_level"),
};

export const proteinTxt = {
  good: i18n.t("protein_good_level"),
  normal: i18n.t("protein_good_plus_level"),
  warning: i18n.t("protein_warning_level"),
  caution: i18n.t("protein_caution_level"),
  danger: i18n.t("protein_danger_level"),
};

export const phTxt = {
  normal: i18n.t("ph_normal_level"),
  warning: i18n.t("ph_warning_level"),
};

export const ketoneTxt = {
  normal: i18n.t("ketone_normal_level_txt"), // 적절
  caution_plus_minus: i18n.t("ketone_caution_level_plus_minus_txt"), // 주의(+/-)
  caution_plus: i18n.t("ketone_caution_level_plus_txt"), // 주의(+)
  warning: i18n.t("ketone_warning_level_txt"), // 경고
  danger: i18n.t("ketone_danger_level_txt"), // 위험
};

export const leukocytesTxt = {
  normal: i18n.t("leukocytes_good_level"), // 적절
  caution: i18n.t("leukocytes_warning_level"), // 주의
  warning: i18n.t("leukocytes_caution_level"), // 경고
  danger: i18n.t("leukocytes_danger_level"), // 위험
};

export const bilirubinTxt = {
  normal: i18n.t("bilirubin_good_level"), // 적절
  caution: i18n.t("bilirubin_caution_level"), // 주의
  warning: i18n.t("bilirubin_warning_level"), // 경고
  danger: i18n.t("bilirubin_danger_level"), // 위험
};

export const urobilinogenTxt = {
  normal: i18n.t("urobilinogen_good_level"), // 적절
  caution: i18n.t("urobilinogen_caution_level"), // 주의
  warning: i18n.t("urobilinogen_warning_level"), // 경고
  danger: i18n.t("urobilinogen_danger_level"), // 위험
};

export const nitriteTxt = {
  normal: i18n.t("nitrite_good_level"), // 적절
  danger: i18n.t("nitrite_danger_level"), // 위험
};

export const sgTxt = {
  good: i18n.t("sg_good_level"), // 적절
  normal: i18n.t("sg_normal_level"), // 적절
  caution: i18n.t("sg_caution_level"), // 주의
  warning: i18n.t("sg_warning_level"), // 경고
  danger: i18n.t("sg_danger_level"), // 위험
};

export const newObj = {
  blood: bloodTxt,
  leukocytes: leukocytesTxt,
  bilirubin: bilirubinTxt,
  urobilinogen: urobilinogenTxt,
  nitrite: nitriteTxt,
  sg: sgTxt,
};
