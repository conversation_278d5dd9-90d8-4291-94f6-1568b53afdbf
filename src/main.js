import Vue from "vue";
import App from "./App.vue";
import vuetify from "./plugins/vuetify";
import router from "./router";
import store from "./store";
import webview from "./service/webview";
import native from "./service/native";
import VueScrollPicker from "vue-scroll-picker";
import CircularCountDownTimer from "vue-circular-count-down-timer";
import Navigation from "./components/Common/Navigation.vue";
import Background from "./components/Common/Background.vue";
import "vue-scroll-picker/dist/style.css";
import i18n from "./i18n";
import VueApexCharts from "vue-apexcharts";

Vue.config.productionTip = false;

Vue.use(CircularCountDownTimer);
Vue.use(VueScrollPicker);
window.Native = native;
window.Webview = webview;

Vue.use(VueApexCharts);

Vue.component("apexchart", VueApexCharts);
Vue.component("navigation", Navigation);
Vue.component("background", Background);
window.Kakao.init("41625f45c9ccefeffa035b8a0d7a0417");

Vue.config.productionTip = false;
// Vue.prototype.isIos = navigator.userAgent.toLowerCase().indexOf("iphone") > -1;
Vue.prototype.isIos = navigator.platform.includes("Mac");

new Vue({
  vuetify,
  store,
  router,
  i18n,
  created() {},
  render: (h) => h(App),
}).$mount("#app");
