import Vue from "vue";
import VueI18n from "vue-i18n";

Vue.use(VueI18n);

function loadLocaleMessages() {
  const locales = require.context("./locales", true, /[A-Za-z0-9-_,\s]+\.json$/i);
  const messages = {};
  locales.keys().forEach((key) => {
    const matched = key.match(/([A-Za-z0-9-_]+)\./i);
    if (matched && matched.length > 1) {
      const locale = matched[1];
      messages[locale] = locales(key);
    }
  });
  return messages;
}

// get the URL query string parameters
// const params = new URLSearchParams(window.location.search);
// let lang = "";

// // check if the lang parameter is present
// if (params.has("lang")) {
//   // set the locale to the language code specified in the URL
//   lang = params.get("lang");
// }
// console.log("params lang:", lang);

// const userLanguage = localStorage.getItem("locale");

// const userLocale = navigator.language || navigator.languages[0] || "en";
// const language = navigator.language.split(/-|_/);
// console.log(navigator.language, userLocale);

// export default new VueI18n({
//   locale: language.includes("CN") ? "cn" : language[0] === "en" ? language[0] : userLocale,
//   // locale: "cn",
//   fallbackLocale: "en",
//   messages: loadLocaleMessages(),
// });

const userLocale = navigator.language || navigator.languages[0] || "en";
const language = navigator.language.split(/-|_/);
console.log(navigator.language, userLocale);

export default new VueI18n({
  locale: language.includes("CN") ? "cn" : language[0] === "en" ? language[0] : userLocale,
  // locale: "cn",
  fallbackLocale: "en",
  messages: loadLocaleMessages(),
});
