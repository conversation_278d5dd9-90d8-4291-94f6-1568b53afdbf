<template>
  <div class="urine-type-container">
    <!-- 한글일 때 -->
    <div class="urine-type isKo" :class="level === null && 'no-result-data'" v-if="isKo">
      <div class="letter" v-for="(char, index) in getUrineTypeArr" :key="index" v-html="char"></div>
    </div>
    <!-- 영어일 때 -->
    <div class="urine-type isEn" :class="level === null && 'no-result-data'" v-else>
      <div class="letter" v-for="(char, index) in getUrineTypeArr" :key="index" v-html="char"></div>
    </div>
    <div class="level-container">
      <div
        class="urine-image-container"
        :class="{ active: isActive && level !== null }"
        ref="imageContainer"
      >
        <!-- alt 텍스트에는 전체 문자열(joined) 사용 -->
        <img :src="urineImage" :alt="getUrineTypeString" width="30px" />
      </div>
      <div
        v-if="isKo"
        class="col-items status-text status-ko"
        :class="getClassNameForResult"
        @click="handleMoveHistoryPageClick"
      >
        <pre v-html="getStatusKoText" />
      </div>
      <div
        v-else
        class="col-items status-text"
        :class="getClassNameForResult"
        @click="handleMoveHistoryPageClick"
      >
        <pre class="status-en" v-html="getStatusEnText" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    type: String,
    level: {
      type: [Number || null],
      default: null,
    },
    urineImage: String,
    isComplete: Boolean,
  },

  data() {
    return {
      isKo: true,
      activeClassName: "active",
    };
  },

  watch: {
    isComplete: {
      immediate: true,
      handler(newValue) {
        if (!newValue && newValue !== null) {
          this.$nextTick(() => {
            this.triggerAnimation();
          });
        }
      },
    },
    "$store.state.showCompleteModal"() {
      // isActive 변경 감지
      if (!this.isActive) {
        this.triggerAnimation();
      }
    },
  },

  computed: {
    isActive() {
      return !this.isComplete;
    },
    // alt 속성 등에서 사용할 원본 문자열 (공백 없이)
    getUrineTypeString() {
      const MAX_LENGTH = window.innerWidth <= 350 ? 4 : 5;
      const typeMap = {
        blood: this.$i18n.t("blood"),
        glucose: this.$i18n.t("glucose"),
        protein: this.$i18n.t("protein"),
        ph: this.$i18n.t("ph"),
        ketone: this.$i18n.t("ketone"),
        leukocytes: this.$i18n.t("leukocytes"),
        bilirubin: this.$i18n.t("bilirubin"),
        urobilinogen: this.$i18n.t("urobilinogen"),
        nitrite: this.$i18n.t("nitrite"),
        sg: this.$i18n.t("S.G"),
      };
      const typeText = typeMap[this.type] || "";
      const trimTypeText = typeText.replace(/\s+/g, "");

      if (trimTypeText.length >= MAX_LENGTH) {
        return trimTypeText.slice(0, MAX_LENGTH - 1) + "..";
      }
      return trimTypeText;
    },
    // 각 글자를 배열로 만들어 템플릿에서 개별 렌더링
    getUrineTypeArr() {
      const MAX_LENGTH = window.innerWidth <= 350 ? 4 : 5;
      const typeMap = {
        blood: this.$i18n.t("blood"),
        glucose: this.$i18n.t("glucose"),
        protein: this.$i18n.t("protein"),
        ph: this.$i18n.t("ph"),
        ketone: this.$i18n.t("ketone"),
        leukocytes: this.$i18n.t("leukocytes"),
        bilirubin: this.$i18n.t("bilirubin"),
        urobilinogen: this.$i18n.t("urobilinogen"),
        nitrite: this.$i18n.t("nitrite"),
        sg: this.$i18n.t("S.G"),
      };

      if (this.type === "ph") {
        return ["p&nbsp;&nbsp;H"];
      }

      if (!this.isKo && this.type === "sg") {
        return ["S .", "G"];
      }

      const typeText = typeMap[this.type] || "";
      const trimTypeText = typeText.replace(/\s+/g, "");
      let chars = [...trimTypeText];

      if (chars.length >= MAX_LENGTH) {
        // 최대 길이 초과 시 마지막 요소 대신 '..' 추가
        chars = chars.slice(0, MAX_LENGTH - 2).concat("..");
      }
      return chars;
    },
    getStatusKoText() {
      return this.getStatus().replace(/\s+/g, "").split("").join(" ");
    },
    getStatusEnText() {
      return this.getStatus().replace(/\s+/g, "").split("").join("");
    },
    getClassNameForResult() {
      const className = [];
      const getScoreResult = this.getStatus();
      const level = this.level;

      if (level === null) {
        className.push("blank-txt");
      }

      if (getScoreResult === this.$i18n.t("main_warning_level")) {
        className.push("warning-txt");
      } else if (getScoreResult === this.$i18n.t("main_caution_level")) {
        className.push("caution-txt");
      } else if (getScoreResult === this.$i18n.t("main_danger_level")) {
        className.push("danger-txt");
      } else if (getScoreResult === this.$i18n.t("main_good_level")) {
        className.push("good-txt");
      } else if (getScoreResult === this.$i18n.t("main_normal_level")) {
        className.push("normal-txt");
      }

      return className.join(" ");
    },
  },

  methods: {
    triggerAnimation() {
      const imageContainer = this.$refs.imageContainer;

      if (imageContainer && this.isActive && this.level !== null) {
        imageContainer.classList.remove("active-waterdrop-ic");

        void imageContainer.offsetWidth;

        imageContainer.classList.add("active-waterdrop-ic");
      }
    },
    handleMoveHistoryPageClick() {
      const urineType = this.type;
      const ROUTES = {
        blood: 1,
        ph: 2,
        protein: 3,
        glucose: 4,
        ketone: 5,
        leukocytes: 6,
        bilirubin: 7,
        urobilinogen: 8,
        nitrite: 9,
        sg: 10,
      };
      Object.freeze(ROUTES);
      const tab = ROUTES[urineType];

      // tooltipIndex는 이미 Home.vue에서 설정되어 있음 (response.result.length - 1)

      this.$router.push({
        path: `/home/<USER>
        query: { tab },
      });
    },
    getStatus() {
      const urineType = this.type;
      const level = this.level;
      const levelText = this.getLevel(urineType, level);

      if (levelText === "empty") {
        return this.$i18n.t("none");
      }

      return this.$i18n.t(`main_${levelText}_level`);
    },
    getLevel(type, level) {
      const scores = ["normal", "warning", "caution", "danger"];

      if (level === null) {
        return "empty";
      }

      switch (type) {
        case "blood":
          return scores[level - 1];
        case "ketone": {
          if (level === 3) {
            return "warning";
          }
          if (level >= 4) {
            return "danger";
          }
          return scores[level - 1];
        }
        case "protein": {
          if (level === 2) {
            return "normal";
          } else if (level > 4) {
            return "danger";
          }
          return scores[level - 1];
        }
        case "glucose": {
          if (level === 2) {
            return "normal";
          } else if (level >= 5) {
            return "danger";
          }
          return scores[level - 1];
        }
        case "ph": {
          if (level === 5) {
            return "warning";
          }
          return "normal";
        }
        case "leukocytes":
          return scores[level - 1];
        case "bilirubin":
          return scores[level - 1];
        case "urobilinogen": {
          if (level <= 2) {
            return "normal";
          }
          return scores[level - 2];
        }
        case "nitrite":
          if (level === 1) {
            return "normal";
          }
          return "danger";
        case "sg":
          if (level <= 2) {
            return "normal";
          }
          if (level === 3) {
            return "warning";
          }
          if (level === 4 || level === 5) {
            return "caution";
          }
          return "danger";
        default:
          return "empty";
      }
    },
  },
  mounted() {
    this.isKo = this.$i18n.locale.includes("ko") || this.$i18n.locale.includes("cn");
  },
};
</script>

<style lang="scss" scoped>
.urine-type-container {
  min-width: 120px;
  width: auto;
  max-width: 200px;
  display: flex;
  align-items: center;
}

.no-result-data {
  color: #a7a7a7;
}

.urine-type-container:nth-child(odd) {
  justify-content: space-between; /* 왼쪽 정렬 */
  text-align: start;
}

.urine-type-container:nth-child(even) {
  justify-content: flex-end; /* 오른쪽 정렬 */
  text-align: end;
}

/* 수정된 .urine-type: flex 컨테이너로 세로 정렬 */
.urine-type {
  width: 46px;
  display: flex;
  justify-content: space-around;
  line-height: 17.15px;
  /* margin-right: 15px; */
  display: flex;
}

.urine-type:has(:only-child) {
  justify-content: center;
}

.urine-type:nth-child(even) {
  margin-right: 100px;
}

.level-container {
  display: flex;
  align-items: center;
}

/* 각 글자 요소 */
.letter {
  width: fit-content;
  text-align: center;
}

.isKo {
  font-family: Noto Sans KR !important;
  font-weight: 500;
  font-size: 16px;
  line-height: 20.27px;
  letter-spacing: -0.1em;
}

.isEn {
  font-family: GilroyBold !important;
  font-size: 16px;
  line-height: 17.15px;
  letter-spacing: -0.1em;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.status-ko {
  font-family: Noto Sans KR !important;
  text-align: center;
  display: flex;
  justify-content: center;
  font-size: 16px;
  font-weight: 700;
  padding: 0.188rem 0.438rem;
}

.status-en {
  font-family: GilroyBold !important;
  font-size: 14px;
  line-height: 14.7px;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  display: flex;
  justify-content: center;
  letter-spacing: -0.5px;
}

.status-text {
  opacity: 0.8;
  display: flex;
  align-items: center;
  border: 1px solid #ededed;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
}

.status-text > pre {
  vertical-align: middle;
}

.col-items {
  min-width: 55px;
  height: 28px;
  text-align: center;
  display: flex;
  justify-content: center;
  margin-left: 10px;
}

.urine-image-container {
  max-width: 30px;
}

.normal-txt {
  color: #00bb00;
  border: 1px solid #00bb00 !important;
}

.warning-txt {
  color: #ffcc00;
  border: 1px solid #ffcc00 !important;
}

.ketone-normal-txt {
  color: #00bb00;
  border: 1px solid #00bb00 !important;
}

.caution-txt {
  color: #ff6600;
  border: 1px solid #ff6600 !important;
}

.danger-txt {
  color: #646464;
  border: 1px solid #646464 !important;
}

.blank-txt {
  color: #a7a7a7;
  border: 1px solid #a7a7a7 !important;
}

.good-txt {
  color: #41d8e6;
  border: 1px solid #41d8e6 !important;
}

.active-waterdrop-ic {
  opacity: 0;
  animation: bounce-in-top 0.7s forwards !important;
}

@keyframes bounce-in-top {
  0% {
    transform: translateY(-100px);
    animation-timing-function: ease-in;
    opacity: 0;
  }
  40% {
    transform: translateY(0);
    animation-timing-function: ease-in;
    opacity: 1;
  }
  90% {
    transform: translateY(-20px);
    animation-timing-function: ease-out;
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    animation-timing-function: ease-out;
    opacity: 1;
  }
}
</style>
