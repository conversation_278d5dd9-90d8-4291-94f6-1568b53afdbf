<template>
  <div>
    <LoadingPageVue v-if="showLoading" />
    <ErrorModal
      :error="content"
      v-if="showResultModal"
      type="analysis"
      @isClicked="reshootHandler"
    />
    <div class="nav-section">
      <div class="exam-nav__wrapper">
        <div class="close-btn__box">
          <img
            v-if="page === 'intro'"
            src="@/assets/images_assets/icons/left-arrow-ic.png"
            alt=""
            @click="goToHome"
            class="left-arrow-ic"
          />
          <img
            v-else-if="page === 'guide'"
            src="@/assets/images_assets/icons/x-circle-ic.png"
            alt=""
            @click="goToGuide"
            class="x-circle-ic"
          />
          <img
            v-else
            src="@/assets/images_assets/icons/x-circle-ic.png"
            alt=""
            @click="goToExamIntro"
            class="x-circle-ic"
          />
        </div>
        <!-- <div class="skip-btn__box" v-if="page === 'guide1'" @click="goToVideo">영상 다시보기</div>
        <div class="skip-btn__box" v-if="page === 'guide2'" @click="goToVideo">영상 다시보기</div> -->
        <div v-if="page !== 'guide'" class="skip-btn__box" @click="goToCamera">
          <!-- <img src="@/assets/images_assets/icons/photo_camera-ic.png" alt="camera-ic" /> -->
          <div>📷 {{ $t("take_urine_test_btn") }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getRefreshToken, setExp } from "@/api/auth/tokenModules";
import LoadingPageVue from "./LoadingPage.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";

export default {
  components: {
    LoadingPageVue,
    ErrorModal,
  },
  props: {
    page: String,
    startHeaderCamera: Boolean,
  },
  data() {
    return {
      // showLoading: false,
      // showResultModal: false,
      content: "",
      eventData: null,
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },
  computed: {
    computedValue() {
      if (this.eventData) {
        return this.eventData.payload.result;
      }
      return null;
    },
    showLoading() {
      return this.$store.state.showExamLoading;
    },
    showResultModal() {
      return this.$store.state.showExamErrorModal;
    },
  },
  watch: {
    computedValue(newVal) {
      // console.log(newVal);
    },
    startHeaderCamera(newVal) {
      this.goToCamera();
    },
  },

  methods: {
    goToHome() {
      this.$router.push("/home");
    },
    goToExamIntro() {
      // this.$router.push("/exam/intro");
      this.$router.push("/home");
    },
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId =
        subjects !== null ? subjects[selectedId].id : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    async goToCamera() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      // this.listener();
      try {
        this.$emit("goToCamera", true);
        const subjectId = this.getSubjectId();
        const token = await getRefreshToken();
        localStorage.auth = token;
        this.$store.commit("SET_TOKEN", token);
        setExp(token);

        const message = {
          accessToken: token,
          subjectId: subjectId,
          description: this.$i18n.t("analysis_description"),
          buttonText: this.$i18n.t("direct_shot"),
          previewDescription: this.$i18n.t("shot_confirm_description"),
          previewOk: this.$i18n.t("yes"),
          previewCancel: this.$i18n.t("no"),
        };

        Webview.requestCamera(message);
        this.$store.commit("setShowExamLoading", true);
        this.$nextTick(() => this.listener());
      } catch (err) {
        console.error(err);
      }
    },
    listener() {
      this.isIos
        ? window.addEventListener("message", this.handleMessage)
        : document.addEventListener("message", this.handleMessage);
    },
    handleMessage(e) {
      const data = JSON.parse(e.data);
      // alert(data.payload.action);
      // alert(data.payload.result);
      if (data.payload.result === "CANCLED") this.$store.commit("setShowExamLoading", false);
      if (data.payload.action === "REQ-TAKE-SHEET") this.examResultHandler(data.payload.result);
    },

    // error modal emit
    reshootHandler(fromChild) {
      if (fromChild) {
        this.goToCamera();
        this.$store.commit("setShowExamErrorModal", false);
      }
    },
    examResultHandler(result) {
      // alert(result);
      switch (result) {
        case "QR": {
          this.content = this.$i18n.t("qr_error_msg");
          this.$store.commit("setShowExamErrorModal", true);
          break;
        }
        case "REF": {
          this.content = this.$i18n.t("qr_error_msg");
          this.$store.commit("setShowExamErrorModal", true);
          break;
        }
        case "QR_BBOX": {
          this.content = this.$i18n.t("qr_error_msg");
          this.$store.commit("setShowExamErrorModal", true);
          break;
        }
        case "STRIP": {
          this.content = this.$i18n.t("strip_error_msg");
          this.$store.commit("setShowExamErrorModal", true);
          break;
        }
        case "SHADOW": {
          this.content = this.$i18n.t("shadow_error_msg");
          this.$store.commit("setShowExamErrorModal", true);
          break;
        }
        case "NOSTRIP": {
          this.content = this.$i18n.t("nostrip_error_msg");
          this.$store.commit("setShowExamErrorModal", true);
          break;
        }
        case "UNKNOWN": {
          this.content = this.$i18n.t("unknown_error_msg");
          this.$store.commit("setShowExamErrorModal", true);
          break;
        }
        case "CANCELED": {
          this.$store.commit("setShowExamLoading", true);
          break;
        }
        case "SUCCESS": {
          this.$store.commit("completeAlert");
          this.$store.commit("setShowExamLoading", false);
          this.$router.push("/home?analysis=done");
          break;
        }
        default: {
          this.content = this.$i18n.t("unknown_error_msg");
          this.$store.commit("setShowExamErrorModal", true);
          this.$store.commit("setShowExamLoading", false);

          break;
        }
      }
      // this.$store.commit("setShowExamLoading", false);
    },
    // goToVideo() {
    //   this.$router.push("/exam/video");
    // },
    goToGuide() {
      this.$router.push("/mypage/guide");
    },
  },
  beforeDestroy() {
    window.removeEventListener("message", this.handleMessage);
    document.removeEventListener("message", this.handleMessage);
    this.$store.commit("setShowExamLoading", false);
    this.$store.commit("setShowExamErrorModal", false);
  },
};
</script>

<style lang="scss" scoped>
.nav-section {
  width: 100%;
  position: fixed;
  z-index: 99;
  top: 0;
  padding-top: 50px;
  padding-bottom: 20px;
}
.exam-nav__wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 10px 30px;
  background-color: transparent;
}
.close-btn__box {
  width: 30px;
  display: flex;
}
.left-arrow-ic {
  width: 12px;
  height: 20px;
}

.x-circle-ic {
  width: 30px;
}
.skip-btn__box {
  background: #ffffff;
  box-shadow: 1px 2px 2px rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  letter-spacing: -0.05em;
  font-size: 14px;
  font-weight: 500;
  padding: 5px 10px;
  img {
    width: 25px;
  }
}
</style>
