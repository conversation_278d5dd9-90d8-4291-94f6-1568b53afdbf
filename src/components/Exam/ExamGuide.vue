<template>
  <div class="guide1-content-section">
    <h1
      class="guide-title"
      :style="{
        fontWeight: isKo ? 'bold' : 'normal',
        fontFamily: !isKo ? 'GilroyBold' : 'Noto Sans KR',
      }"
    >
      {{ $t("urine_sampling") }}
    </h1>
    <p class="select-text">{{ $t("select_urine_collection") }}</p>

    <!-- guide description -->
    <div class="guide-contents">
      <!-- 직접 채취법 -->
      <article class="guide-article">
        <div class="guide-contents__wrapper">
          <div class="img__wrapper">
            <img src="@/assets/images_assets/icons/check-ic.png" alt="check-icon" loading="lazy" />
          </div>
          <div class="guide-text" :style="{ fontWeight: !isKo ? 'bold' : 'normal' }">
            {{ $t("guide_title_first") }}
          </div>
        </div>
        <nav class="description-section" v-for="guide in directUrineGuides" :key="guide.id">
          <p class="guide-description">
            {{ guide.description }}
          </p>
          <div class="guide-img__wrapper">
            <img
              :src="guide.img"
              class="guide_3"
              alt="attach strip"
              loading="lazy"
              loop="infinite"
            />
          </div>
        </nav>
      </article>
      <article class="guide-article">
        <!-- 간접 채취법 -->
        <div class="guide-contents__wrapper">
          <div class="img__wrapper">
            <img src="@/assets/images_assets/icons/check-ic.png" alt="check-icon" loading="lazy" />
          </div>
          <div class="guide-text" :style="{ fontWeight: !isKo ? 'bold' : 'normal' }">
            {{ $t("guide_title_second") }}
          </div>
        </div>
        <nav class="description-section" v-for="guide in indirectUrineGuides" :key="guide.id">
          <p class="guide-description">
            {{ guide.description }}
          </p>
          <div class="guide-img__wrapper">
            <img
              :src="guide.img"
              class="guide_3"
              alt="attach strip"
              loading="lazy"
              loop="infinite"
            />
          </div>
        </nav>
      </article>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      isKo: this.$i18n.locale.includes("ko") ?? false, // 한국어냐 아니냐
      urineGuides: [
        // 가이드
        {
          id: 1,
          type: "direct",
          description: this.$t("guide_attach_strip_description"),
          img: require(`@/assets/gif/attach_strip_no-urine.gif`),
        },
        {
          id: 2,
          type: "direct",
          description: this.$t("guide_during_urine_description"),
          img: require("@/assets/gif/direct_sampling.gif"),
        },
        {
          id: 3,
          type: "indirect",
          description: this.$t("guide_buried_description"),
          img: require("@/assets/gif/indirect_sampling.gif"),
        },
        {
          id: 4,
          type: "indirect",
          description: this.$t("guide_attach_strip_description"),
          img: require("@/assets/gif/attach_strip_urine.gif"),
        },
      ],
    };
  },
  computed: {
    directUrineGuides() {
      return this.urineGuides.filter((guide) => guide.type === "direct");
    },
    indirectUrineGuides() {
      return this.urineGuides.filter((guide) => guide.type === "indirect");
    },
  },
  methods: {
    goToCamera() {
      this.$router.push({ path: "/exam/wait" });
    },
    goToHome() {
      this.$router.push({ path: "/home" });
    },
  },
};
</script>

<style lang="scss" scoped>
.select-text {
  text-align: start;
  margin: 0;
  font-weight: 400;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: -2%;
  vertical-align: middle;
}

.guide-article {
  &:first-of-type {
    margin-top: 1.875rem;
  }

  &:nth-child(2) {
    margin-top: 5vh;
  }
}

.guide1-content-section {
  width: 100%;
  height: 100vh;
  padding: 100px 30px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.guide-contents {
  // height: 100%;
  overflow-y: scroll;
  overflow-x: hidden;
}

.guide-title {
  margin-top: 5vh;
  font-size: 26px;
  line-height: 29px;
  letter-spacing: -0.03em;
  text-align: start;
  margin-bottom: 10px;
}

.guide-contents__wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  letter-spacing: -0.03em;
  margin: 0;
}

.img__wrapper {
  img {
    width: 18px;
  }
}
.guide-text {
  font-size: 22px;
  font-weight: 500;
  margin-left: 10px;
  /* font-family: GilroyBold !important; */
}

.guide-description {
  text-align: left;
  padding: 5px 0 0 26px;
  letter-spacing: -0.7px;
}

.guide-img__wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 2vh 0;
  // height: 80vh;
  img {
    width: 90%;
    object-fit: contain;
  }
}

.guide_3 {
  width: 220px;
}
</style>
