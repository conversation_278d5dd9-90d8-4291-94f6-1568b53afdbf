<template>
  <div>
    <div class="stage-guide__bg-modal">
      <div class="modal-window">
        <div class="guide-content__container">
          <div class="content-title__wrapper">
            <div class="content-header__closeBtn">
              <div @click="closeGuideModal">
                <v-icon>$x_circle_solid</v-icon>
              </div>
            </div>

            <div class="content-header__title">
              <div class="content-title">{{ title }}</div>
              <div class="content-subimg-items" v-if="this.title.length < 8">
                <div class="subimg-item">
                  <img :src="setImage.color1" alt="" loading="lazy" />
                </div>
                <div class="subimg-item">
                  <img :src="setImage.color2" alt="" loading="lazy" />
                </div>
                <div
                  class="subimg-item"
                  v-if="type === 'ph' && title === this.$i18n.t('normal')"
                >
                  <img :src="setImage.color3" alt="" loading="lazy" />
                </div>
                <div
                  class="subimg-item"
                  v-if="type === 'ph' && title === this.$i18n.t('normal')"
                >
                  <img :src="setImage.color4" alt="" loading="lazy" />
                </div>
              </div>
            </div>
            <div class="zoom-btn" @click="textZomeControlView">
              <span class="small">{{ $t("font_size") }}</span>
              <span class="large">{{ $t("font_size") }}</span>
            </div>
          </div>
          <template v-if="showZoomControlView">
            <div class="slider__wrapper">
              <div class="smallText">{{ $t("font_size") }}</div>
              <v-slider
                color="#C9F4F8"
                track-color="#fff"
                track-fill-color="#fff"
                v-model="slider"
                step="1"
                :max="4"
                ticks="always"
              ></v-slider>
              <div class="largeText">{{ $t("font_size") }}</div>
            </div>
          </template>
          <div class="guide-txt__wrapper">
            <div class="guide-txt" v-html="convertInfoTxt" ref="guideTxt"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  normalStripImg,
  warningStripImg,
  cautionStripImg,
  dangerStripImg,
  enterStripImg,
  exertionStripImg,
} from "./utils/computeChart";
export default {
  props: {
    type: String,
    stage: String,
  },
  data() {
    return {
      setImage: {},
      title: "",
      levelInfoTxt: "",
      slider: 0,
      showZoomControlView: false,
    };
  },
  methods: {
    textZomeControlView() {
      if (this.showZoomControlView) {
        this.showZoomControlView = false;
      } else {
        this.showZoomControlView = true;
      }
    },
    closeGuideModal() {
      // document.body.classList.remove("no-scroll");
      this.$store.commit("closeGuideModal");
    },
    getLevelValue(type, level) {
      // console.log(type, level);
      console.log(type, level);

      switch (level) {
        case this.$i18n.t("normal"):
          return type === "ketone"
            ? this.$i18n.t("ketone_normal_level_modal")
            : this.$i18n.t(`${type}_good_level_modal`);
        case this.$i18n.t("warning"): {
          if (type === "ketone") {
            return this.$i18n.t(`${type}_warning_level_modal`);
          }
          return this.$i18n.t(`${type}_warning_level_modal`);
        }
        case this.$i18n.t("good"):
          return this.$i18n.t(`${type}_warning_level_modal`);
        case this.$i18n.t("caution"):
          return this.$i18n.t(`${type}_caution_level_modal`);
        case this.$i18n.t("danger"):
          return this.$i18n.t(`${type}_danger_level_modal`);
        case this.$i18n.t("ketone_warning_level_plus_minus"):
          return this.$i18n.t(`${type}_warning_level_plus_minus_modal`);
        case this.$i18n.t("ketone_warning_level_plus"):
          return this.$i18n.t(`${type}_warning_level_plus_modal`);
        case "healthInfo":
          return this.$i18n.t(`${type}_info`);
        case this.$i18n.t("blood_disease"):
          return this.$i18n.t("blood_disease_info");
        case this.$i18n.t("ph_disease"):
          return this.$i18n.t("ph_disease_info");
        case this.$i18n.t("protein_disease"):
          return this.$i18n.t("protein_disease_info");
        case this.$i18n.t("glucose_disease"):
          return this.$i18n.t("glucose_disease_info");
        case this.$i18n.t("ketone_disease"):
          return this.$i18n.t("ketone_disease_info");
        case this.$i18n.t("sg_disease"):
          return this.$i18n.t("sg_disease_info");
        case this.$i18n.t("urobilinogen_disease"):
          return this.$i18n.t("urobilinogen_disease_info");
        case this.$i18n.t("bilirubin_disease"):
          return this.$i18n.t("bilirubin_disease_info");
        case this.$i18n.t("nitrite_disease"):
          return this.$i18n.t("nitrite_disease_info");
        case this.$i18n.t("leukocytes_disease"):
          return this.$i18n.t("leukocytes_disease_info");
        default:
          console.error(`There is no level '${level}' in this case.`);
          return "";
      }

      // if (level === this.$i18n.t("normal") && type === "ketone") return this.$i18n.t("ketone_normal_level_modal");
      // if (level === this.$i18n.t("normal")) return this.$i18n.t(`${type}_good_level_modal`);
      // // if (level === this.$i18n.t("exertion")) return this.$i18n.t(`${type}_good_level_modal`);
      // // if (level === this.$i18n.t("enter")) return this.$i18n.t(`${type}_enter_level_modal`);
      // if (level === this.$i18n.t("ketone_warning_level_plus_minus"))
      //   return this.$i18n.t(`${type}_warning_level_plus_minus_modal`);
      // if (level === this.$i18n.t("ketone_warning_level_plus")) return this.$i18n.t(`${type}_warning_level_plus_modal`);
      // if (level === this.$i18n.t("ketone_warning_level_plus")) return this.$i18n.t(`${type}_warning_level_modal`);
      // if (level === this.$i18n.t("caution") && type === "ketone") return this.$i18n.t(`${type}_caution_modal`);
      // if (level === this.$i18n.t("warning")) return this.$i18n.t(`${type}_warning_level_modal`);
      // if (level === this.$i18n.t("good")) return this.$i18n.t(`${type}_warning_level_modal`);
      // if (level === this.$i18n.t("caution")) return this.$i18n.t(`${type}_caution_level_modal`);
      // if (level === this.$i18n.t("danger")) return this.$i18n.t(`${type}_danger_level_modal`);
      // if (level === "healthInfo") {
      //   if (type === "blood") return this.$i18n.t("blood_info");
      //   if (type === "protein") return this.$i18n.t("protein_info");
      //   if (type === "ph") return this.$i18n.t("ph_info");
      //   if (type === "glucose") return this.$i18n.t("glucose_info");
      //   if (type === "ketone") return this.$i18n.t("ketone_info");
      // }
    },
  },
  computed: {
    convertInfoTxt() {
      return this.levelInfoTxt;
      // .replaceAll("\n", "<br /><br/>");
    },

    returnSliderValue() {
      return this.slider;
    },
  },
  watch: {
    slider: function (newVal, oldVal) {
      const fontSize = 18 + newVal * 2;
      this.$refs.guideTxt.style.fontSize = `${fontSize}px`;
    },
  },
  mounted() {
    this.title = this.$store.state.cardTitle;
    if (this.type === "ketone") {
      if (
        this.$store.state.cardTitle ===
          this.$i18n.t("ketone_good_level_minus") ||
        this.$store.state.cardTitle ===
          this.$i18n.t("ketone_warning_level_plus")
      ) {
        this.setImage = normalStripImg(this.type);
      }
      if (
        // 주의(+/-), 진입
        this.$store.state.cardTitle ===
        this.$i18n.t("ketone_warning_level_plus_minus")
      ) {
        this.setImage = enterStripImg(this.type);
      }
      if (this.$store.state.cardTitle === this.$i18n.t("normal")) {
        this.setImage = exertionStripImg(this.type);
      }
      if (this.$store.state.cardTitle === this.$i18n.t("caution")) {
        this.setImage = cautionStripImg(this.type);
      }
      if (
        this.$store.state.cardTitle === this.$i18n.t("warning") ||
        this.$store.state.cardTitle === this.$i18n.t("danger")
      ) {
        this.setImage = dangerStripImg(this.type);
      }
    } else {
      if (this.$store.state.cardTitle === this.$i18n.t("normal"))
        this.setImage = normalStripImg(this.type);
      if (this.$store.state.cardTitle === this.$i18n.t("warning"))
        this.setImage = warningStripImg(this.type);
      if (this.$store.state.cardTitle === this.$i18n.t("caution"))
        this.setImage = cautionStripImg(this.type);
      if (this.$store.state.cardTitle === this.$i18n.t("danger"))
        this.setImage = dangerStripImg(this.type);
    }
    // console.log(this.title.includes("질환"));

    console.log("title");
    console.log(this.title);
    console.log(this.type);

    if (this.title.includes("질환") || this.title.includes("disease")) {
      this.levelInfoTxt = this.getLevelValue(this.type, this.title);
    } else if (this.title.includes("?") || this.title.includes("？")) {
      this.levelInfoTxt = this.getLevelValue(this.type, "healthInfo");
    } else {
      this.levelInfoTxt = this.getLevelValue(
        this.type,
        this.$store.state.cardTitle
      );
    }
  },
};
</script>

<style lang="scss" scoped>
.stage-guide__bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999999;
  overflow-y: auto;
  // -webkit-overflow-scrolling: touch;
  padding: 15px 25px;
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  transition: opacity 0.15s linear;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-window {
  background-color: #fff;
  width: 100%;
  box-shadow: 0px 8px 36px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  padding: 15px 20px;
  min-height: 540px;
  height: 75vh;
}
.guide-content__container {
  width: 100%;
}
.content-title__wrapper {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ededed;
  padding: 10px 0;
}
.content-title {
  // font-family: "Noto Sans KR";
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 18px;
  /* identical to box height */
  display: flex;
  align-items: center;
  text-align: center;
  letter-spacing: -0.03em;
}
.content-header__title {
  display: flex;
  padding-left: 10px;
}
.content-subimg-items {
  display: flex;
}
.subimg-item {
  padding: 0px 3px;
}
.subimg-item > img {
  width: 20px;
  margin-top: 7px;
}

.zoom-btn {
  letter-spacing: -0.15em;
  color: #41d8e6;
  font-weight: 900;
}

.small {
  font-size: 14px;
}
.large {
  font-size: 20px;
}
.guide-txt__wrapper {
  width: 100%;
  padding: 10px 0;
}
.guide-txt {
  width: 100%;
  height: 60vh;
  text-align: left;
  // font-family: "Noto Sans KR";
  font-style: normal;
  font-size: 18px;
  line-height: 25px;
  letter-spacing: -0.02em;
  overflow: scroll;
}
.slider__wrapper {
  z-index: 999;
  position: absolute;
  margin-top: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  padding: 10px 15px 5px 15px;
  background-color: #41d8e6;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.slider__wrapper:after {
  border-top: 0px solid transparent;
  border-left: 8px solid transparent;
  border-right: 2px solid transparent;
  border-bottom: 8px solid #41d8e6;
  content: "";
  position: absolute;
  top: -8px;
  left: 89%;
}
.smallText {
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  padding-bottom: 4px;
}
.largeText {
  font-size: 30px;
  font-weight: 700;
  color: #fff;
  padding-left: 7px;
  padding-bottom: 4px;
}
::v-deep .v-messages {
  display: none !important;
}
::v-deep .v-slider__tick {
  border-left: 0.5px solid #fff !important;
  height: 10px !important;
  position: absolute;
  left: 50%;
  margin-left: 0px;
  top: -4px !important;
  background-color: #fff !important;
}
::v-deep .v-slider__thumb {
  width: 25px !important;
  height: 25px !important;
  box-shadow: 2px 2px 5px rgb(0 0 0 / 20%) !important;
}
</style>
