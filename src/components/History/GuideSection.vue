<template>
  <div>
    <div class="guide-section__container">
      <div class="container-items__wrapper">
        <div class="gid-container-items" v-if="type === 'nitrite'">
          <div class="gid-card__item">
            <GuideCard
              :stripImage="stripNormal"
              :cardTitle="normal"
              cardBoxHeight="min-height-140"
            />
          </div>
          <div class="gid-card__item">
            <GuideCard
              :stripImage="stripDanger"
              :cardTitle="danger"
              cardBoxHeight="min-height-140"
            />
          </div>
        </div>
        <div class="gid-container-items" v-else>
          <div class="gid-card__item">
            <GuideCard
              :stripImage="stripNormal"
              :cardTitle="normal"
              cardBoxHeight="min-height-140"
            />
          </div>
          <div class="gid-card__item" v-if="type !== 'ph'">
            <GuideCard
              :stripImage="stripWarning"
              :cardTitle="caution"
              cardBoxHeight="min-height-140"
            />
          </div>
        </div>
        <div class="gid-container-items" v-if="type !== 'nitrite'">
          <div class="gid-card__item">
            <GuideCard
              :stripImage="stripCaution"
              :cardTitle="type === 'ph' ? caution : warning"
              cardBoxHeight="min-height-140"
            />
          </div>
          <div class="gid-card__item" v-if="type !== 'ph'">
            <GuideCard
              :stripImage="stripDanger"
              :cardTitle="danger"
              cardBoxHeight="min-height-140"
            />
          </div>
        </div>
        <!-- <div class="gid-container-items">
          <div class="gid-card__ketone" v-if="type === 'ketone'">
            <GuideCard
              :stripImage="ketoneDangerImg"
              :cardTitle="ketoneDanger"
              cardBoxHeight="min-height-140"
            />
          </div> -->
        <!-- </div> -->
      </div>
      <div class="urineTestItemsInfo-cards__wrapper">
        <div class="gid-card__item">
          <GuideCard :cardTitle="cardTitle" cardBoxHeight="min-height-80" />
        </div>
        <div class="gid-card__item">
          <GuideCard :cardTitle="card2Title" cardBoxHeight="min-height-80" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import GuideCard from "./GuideCard.vue";

export default {
  components: {
    GuideCard,
  },
  props: {
    type: String,
  },
  data() {
    return {
      normal: "",
      warning: "",
      caution: "",
      danger: "",
      stripNormal: "",
      stripWarning: "",
      stripCaution: "",
      stripDanger: "",
      ketoneDangerImg: "",
      ketoneDanger: "",
      cardTitle: "",
      card2Title: "",
    };
  },
  mounted() {
    if (this.type === "blood") {
      this.normal = this.$i18n.t("normal");
      this.warning = this.$i18n.t("warning");
      this.caution = this.$i18n.t("caution");
      this.danger = this.$i18n.t("danger");
      this.cardTitle = this.$i18n.t("what_is_hematuria");
      this.card2Title = this.$i18n.t("blood_disease");

      this.stripNormal = require("@/assets/images/ui_strip_images/blood/normal.png");
      this.stripWarning = require("@/assets/images/ui_strip_images/blood/warning.png");
      this.stripCaution = require("@/assets/images/ui_strip_images/blood/caution.png");
      this.stripDanger = require("@/assets/images/ui_strip_images/blood/danger.png");
    } else if (this.type === "glucose") {
      this.normal = this.$i18n.t("normal");
      this.warning = this.$i18n.t("warning");
      this.caution = this.$i18n.t("caution");
      this.danger = this.$i18n.t("danger");
      this.cardTitle = this.$i18n.t("what_is_glycosuria");
      this.card2Title = this.$i18n.t("glucose_disease");
      this.stripNormal = require("@/assets/images/ui_strip_images/glucose/normal.png");
      this.stripWarning = require("@/assets/images/ui_strip_images/glucose/warning.png");
      this.stripCaution = require("@/assets/images/ui_strip_images/glucose/caution.png");
      this.stripDanger = require("@/assets/images/ui_strip_images/glucose/danger.png");
    } else if (this.type === "protein") {
      this.normal = this.$i18n.t("normal");
      this.warning = this.$i18n.t("warning");
      this.caution = this.$i18n.t("caution");
      this.danger = this.$i18n.t("danger");
      this.cardTitle = this.$i18n.t("what_is_proteinuria");
      this.card2Title = this.$i18n.t("protein_disease");
      this.stripNormal = require("@/assets/images/ui_strip_images/protein/normal.png");
      this.stripWarning = require("@/assets/images/ui_strip_images/protein/warning.png");
      this.stripCaution = require("@/assets/images/ui_strip_images/protein/caution.png");
      this.stripDanger = require("@/assets/images/ui_strip_images/protein/danger.png");
    } else if (this.type === "ph") {
      this.normal = this.$i18n.t("normal");
      this.warning = this.$i18n.t("warning");
      this.caution = this.$i18n.t("caution");
      this.danger = "";
      this.cardTitle = this.$i18n.t("what_is_ph");
      this.card2Title = this.$i18n.t("ph_disease");
      this.stripNormal = require("@/assets/images/ui_strip_images/ph/normal.png");
      this.stripWarning = require("@/assets/images/ui_strip_images/ph/normal.png");
      this.stripCaution = require("@/assets/images/ui_strip_images/ph/warning.png");
      this.stripDanger = require("@/assets/images/ui_strip_images/ph/warning.png");
    } else if (this.type === "ketone") {
      this.normal = this.$i18n.t("normal");
      this.caution = this.$i18n.t("ketone_caution_level_plus_minus");
      this.warning = this.$i18n.t("warning");
      this.danger = this.$i18n.t("danger");
      this.cardTitle = this.$i18n.t("what_is_ketone");
      this.card2Title = this.$i18n.t("ketone_disease");
      this.stripNormal = require("@/assets/images/ui_strip_images/ketone/normal_neg.png");
      this.stripWarning = require("@/assets/images/ui_strip_images/ketone/caution.png");
      this.stripCaution = require("@/assets/images/ui_strip_images/ketone/warning.png");
      this.stripDanger = require("@/assets/images/ui_strip_images/ketone/danger.png");
    } else {
      this.normal = this.$i18n.t("normal");
      this.warning = this.$i18n.t("warning");
      this.caution = this.$i18n.t("caution");
      this.danger = this.$i18n.t("danger");
      this.cardTitle = this.$i18n.t(`what_is_${this.type}`);
      this.card2Title = this.$i18n.t(`${this.type}_disease`);
      this.stripNormal = require(`@/assets/images/ui_strip_images/${this.type}/good.png`);
      if (this.type !== "nitrite") {
        this.stripWarning = require(`@/assets/images/ui_strip_images/${this.type}/caution.png`);
        this.stripCaution = require(`@/assets/images/ui_strip_images/${this.type}/warning.png`);
      }
      this.stripDanger = require(`@/assets/images/ui_strip_images/${this.type}/danger.png`);
    }
  },
};
</script>

<style scoped lang="scss">
.subtitle {
  padding-bottom: 20px;
  font-size: 18px;
  font-weight: 700;
}

.container-title__wrapper {
  width: 100%;
  align-items: center;
  padding: 10px 0px;
}

.guide-section-title {
  width: 100%;
  /* font-family: "Noto Sans KR"; */
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 26px;
  text-align: center;
  letter-spacing: -0.03em;
}

.container-items__wrapper {
  width: 100%;
  padding: 0px 20px;
  margin: auto;
}

.urineTestItemsInfo-cards__wrapper {
  padding-left: 20px;
  padding-right: 20px;
  padding-bottom: 120px;
}

.gid-container-items {
  display: flex;
  width: 100%;
}

.gid-card__item {
  width: 100%;
  padding: 10px;
}

.gid-card__ketone {
  width: 50%;
  padding: 10px;
}
</style>
