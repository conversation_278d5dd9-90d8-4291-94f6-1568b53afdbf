import i18n from "../../../i18n.js";

export const computeResult = (type, value, en, ko, color) => {
  if (type === "blood") {
    if (value === 1) {
      if (en === "en") {
        return i18n.t("normal");
      } else if (ko !== "") {
        return i18n.t("normal");
      } else if (color !== "") {
        return "#00bb00";
      }
    } else if (value === 2) {
      if (en !== "") {
        return i18n.t("warning");
      } else if (ko !== "") {
        return i18n.t("warning");
      } else if (color !== "") {
        return "#ffcc00";
      }
    } else if (value === 3) {
      if (en !== "") {
        return i18n.t("caution");
      } else if (ko !== "") {
        return i18n.t("caution");
      } else if (color !== "") {
        return "#ff6600";
      }
    } else if (value === 4) {
      if (en !== "") {
        return i18n.t("danger");
      } else if (ko !== "") {
        return i18n.t("danger");
      } else if (color !== "") {
        return "#646464";
      }
    }
  } else if (type === "protein" || type === "glucose") {
    if (value === 1 || value === 2) {
      if (en === "en") {
        return i18n.t("normal");
      } else if (ko !== "") {
        return i18n.t("normal");
      } else if (color !== "") {
        return "#00bb00";
      }
    } else if (value === 3) {
      if (en !== "") {
        return i18n.t("warning");
      } else if (ko !== "") {
        return i18n.t("warning");
      } else if (color !== "") {
        return "#ffcc00";
      }
    } else if (value === 4) {
      if (en !== "") {
        return i18n.t("caution");
      } else if (ko !== "") {
        return i18n.t("caution");
      } else if (color !== "") {
        return "#ff6600";
      }
    } else if (value === 5 || value === 6) {
      if (en !== "") {
        return i18n.t("danger");
      } else if (ko !== "") {
        return i18n.t("danger");
      } else if (color !== "") {
        return "#646464";
      }
    }
  } else if (type === "ph") {
    if (
      value === 1 ||
      value === 2 ||
      value === 3 ||
      value === 4 ||
      value === 5
    ) {
      if (en === "en") {
        return i18n.t("normal");
      } else if (ko !== "") {
        return i18n.t("normal");
      } else if (color !== "") {
        return "#00bb00";
      }
    } else if (value === 6) {
      if (en === "en") {
        return i18n.t("warning");
      } else if (ko !== "") {
        return i18n.t("warning");
      } else if (color !== "") {
        return "#ffcc00";
      }
    }
  } else if (type === "ketone") {
    if (value === 1) {
      if (en === "en") {
        return "normal";
      } else if (ko !== "") {
        return i18n.t("normal");
      } else if (color !== "") {
        return "#00bb00";
      }
    } else if (value === 2 || value === 3) {
      if (en !== "") {
        return i18n.t("normal");
      } else if (ko !== "") {
        return i18n.t("normal");
      } else if (color !== "") {
        return "#00bb00";
      }
    } else if (value === 4) {
      if (en !== "") {
        return "good";
      } else if (ko !== "") {
        return i18n.t("ketone_great_level");
      } else if (color !== "") {
        return "#41d8e6";
      }
    } else if (value === 5) {
      if (en !== "") {
        return i18n.t("warning");
      } else if (ko !== "") {
        return i18n.t("warning");
      } else if (color !== "") {
        return "#ffcc00";
      }
    }
  }
};

export const historyResult = (value, en, ko, color) => {
  if (value === 1 || value === 2) {
    if (en === "en") {
      return i18n.t("normal");
    } else if (ko !== "") {
      return i18n.t("normal");
    } else if (color !== "") {
      return "#00bb00";
    }
  } else if (value === 3) {
    if (en !== "") {
      return i18n.t("warning");
    } else if (ko !== "") {
      return i18n.t("warning");
    } else if (color !== "") {
      return "#ffcc00";
    }
  } else if (value === 4) {
    if (en !== "") {
      return i18n.t("caution");
    } else if (ko !== "") {
      return i18n.t("caution");
    } else if (color !== "") {
      return "#ff6600";
    }
  } else if (value === 5 || value === 6) {
    if (en !== "") {
      return i18n.t("danger");
    } else if (ko !== "") {
      return i18n.t("danger");
    } else if (color !== "") {
      return "#646464";
    }
  }
};

export const ketoneResult = (value, en, ko, color) => {
  if (value === 1) {
    if (en === "en") {
      return i18n.t("ketone_good_level_minus");
    } else if (ko !== "") {
      return i18n.t("ketone_good_level_minus");
    } else if (color !== "") {
      return "#00bb00";
    }
  } else if (value === 2 || value === 3) {
    if (en !== "") {
      return i18n.t("ketone_warning_level_plus");
    } else if (ko !== "") {
      return i18n.t("ketone_warning_level_plus");
    } else if (color !== "") {
      return "#00bb00";
    }
  } else if (value === 4) {
    if (en !== "") {
      return i18n.t("ketone_great_level");
    } else if (ko !== "") {
      return i18n.t("ketone_great_level");
    } else if (color !== "") {
      return "#41d8e6";
    }
  } else if (value === 5) {
    if (en !== "") {
      return i18n.t("warning");
    } else if (ko !== "") {
      return i18n.t("warning");
    } else if (color !== "") {
      return "#ffcc00";
    }
  }
};

export const computedYtics = (type) => {
  if (type === "blood")
    return {
      min: 1,
      max: 4,
      tick: {
        format: function(x) {
          if (x === 1) return i18n.t("normal");
          else if (x === 2) return i18n.t("warning");
          else if (x === 3) return i18n.t("caution");
          else if (x === 4) return i18n.t("danger");
          else if (x === 5) return "";
          else return "";
        },
      },
    };
  else if (type === "glucose" || type === "protein")
    return {
      min: 1,
      max: 6,
      tick: {
        format: function(x) {
          if (x === 1) return "";
          else if (x === 2) return i18n.t("normal");
          else if (x === 3) return i18n.t("warning");
          else if (x === 4) return i18n.t("caution");
          else if (x === 5) return i18n.t("danger");
          else if (x === 6) return "";
          else return "";
        },
      },
    };
  else if (type === "ph")
    return {
      min: 1,
      max: 6,
      tick: {
        format: function(x) {
          if (x === 5) return i18n.t("normal");
          else if (x === 6) return i18n.t("warning");
          else return "";
        },
      },
    };
  else if (type === "ketone")
    return {
      min: 1,
      max: 5,
      tick: {
        format: function(x) {
          if (x === 1) return "";
          else if (x === 2) return "";
          else if (x === 3) return i18n.t("normal");
          else if (x === 4) return i18n.t("ketone_great_level");
          else if (x === 5) return i18n.t("warning");
          else return "";
        },
      },
    };
};

export const computeYgrid = (type) => {
  if (type === "blood")
    return {
      lines: [
        {
          value: 0,
          class: "grid",
        },
        {
          value: 1,
          class: "grid1",
        },
        {
          value: 2,
          class: "grid2",
        },
        {
          value: 3,
          class: "grid3",
        },
        {
          value: 4,
          class: "grid4",
        },
      ],
    };
  if (type === "glucose" || type === "protein")
    return {
      lines: [
        {
          value: 0,
          class: "grid",
        },
        {
          value: 1,
          class: "grid1",
        },
        {
          value: 2,
          class: "grid1",
        },
        {
          value: 3,
          class: "grid2",
        },
        {
          value: 4,
          class: "grid3",
        },
        {
          value: 5,
          class: "grid4",
        },
        {
          value: 6,
          class: "grid4",
        },
      ],
    };
  if (type === "ph")
    return {
      lines: [
        {
          value: 0,
          class: "grid",
        },
        {
          value: 1,
          class: "grid1",
        },
        {
          value: 2,
          class: "grid1",
        },
        {
          value: 3,
          class: "grid1",
        },
        {
          value: 4,
          class: "grid1",
        },
        {
          value: 5,
          class: "grid1",
        },
        {
          value: 6,
          class: "grid2",
        },
      ],
    };
  if (type === "ketone")
    return {
      lines: [
        {
          value: 0,
          class: "grid",
        },
        {
          value: 1,
          class: "grid1",
        },
        {
          value: 2,
          class: "grid1",
        },
        {
          value: 3,
          class: "grid1",
        },
        {
          value: 4,
          class: "grid5",
        },
        {
          value: 5,
          class: "grid2",
        },
      ],
    };
};
export const normalStripImg = (value) => {
  if (value === "blood") {
    return {
      color1: require("@/assets/images/exam-colors/blood_1.png"),
      color2: "",
    };
  } else if (value === "glucose") {
    return {
      color1: require("@/assets/images/exam-colors/glucose_1.png"),
      color2: require("@/assets/images/exam-colors/glucose_2.png"),
    };
  } else if (value === "protein") {
    return {
      color1: require("@/assets/images/exam-colors/protein_1.png"),
      color2: require("@/assets/images/exam-colors/protein_2.png"),
    };
  } else if (value === "ketone") {
    return {
      color1: require("@/assets/images/exam-colors/ketone_normal.png"),
      color2: "",
    };
  } else if (value === "ph") {
    return {
      color1: require("@/assets/images/exam-colors/ph_ph5.png"),
      color2: require("@/assets/images/exam-colors/ph_ph6.png"),
      color3: require("@/assets/images/exam-colors/ph_ph7.png"),
      color4: require("@/assets/images/exam-colors/ph_ph8.png"),
    };
  } else if (value === "leukocytes") {
    return {
      color1: require("@/assets/images/exam-colors/leukocytes_1.png"),
    };
  } else if (value === "bilirubin") {
    return { color1: require("@/assets/images/exam-colors/bilirubin_1.png") };
  } else if (value === "urobilinogen") {
    return {
      color1: require("@/assets/images/exam-colors/urobilinogen_plus.png"),
      color2: require("@/assets/images/exam-colors/urobilinogen_minus.png"),
    };
  } else if (value === "nitrite") {
    return {
      color1: require("@/assets/images/exam-colors/nitrite_negative.png"),
    };
  } else if (value === "sg") {
    return {
      color1: require("@/assets/images/exam-colors/sg_normal_plus.png"),
      color2: require("@/assets/images/exam-colors/sg_normal_minus.png"),
    };
  } else {
    return {
      color1: "",
    };
  }
};

export const warningStripImg = (value) => {
  if (value === "blood") {
    return {
      color1: require("@/assets/images/exam-colors/blood_2.png"),
      color2: "",
    };
  } else if (value === "glucose") {
    return {
      color1: require("@/assets/images/exam-colors/glucose_3.png"),
      color2: "",
    };
  } else if (value === "protein") {
    return {
      color1: require("@/assets/images/exam-colors/protein_3.png"),
      color2: "",
    };
  } else if (value === "ketone") {
    return {
      color1: require("@/assets/images/exam-colors/ketone_normal_25.png"),
      color2: "",
    };
  } else if (value === "ph") {
    return {
      color1: require("@/assets/images/exam-colors/ph_ph9.png"),
      color2: "",
    };
  } else if (value === "leukocytes") {
    return {
      color1: require("@/assets/images/exam-colors/leukocytes_2.png"),
    };
  } else if (value === "bilirubin") {
    return { color1: require("@/assets/images/exam-colors/bilirubin_2.png") };
  } else if (value === "urobilinogen") {
    return {
      color1: require("@/assets/images/exam-colors/urobilinogen_2.png"),
    };
  } else if (value === "nitrite") {
    return {
      color1: "",
    };
  } else if (value === "sg") {
    return {
      color1: require("@/assets/images/exam-colors/sg_2.png"),
    };
  }
};

export const cautionStripImg = (value) => {
  if (value === "blood") {
    return {
      color1: require("@/assets/images/exam-colors/blood_3.png"),
      color2: "",
    };
  } else if (value === "glucose") {
    return {
      color1: require("@/assets/images/exam-colors/glucose_4.png"),
      color2: "",
    };
  } else if (value === "protein") {
    return {
      color1: require("@/assets/images/exam-colors/protein_4.png"),
      color2: "",
    };
  } else if (value === "ketone") {
    return {
      color1: require("@/assets/images/exam-colors/ketone_good.png"),
      color2: "",
    };
  } else if (value === "leukocytes") {
    return {
      color1: require("@/assets/images/exam-colors/leukocytes_3.png"),
    };
  } else if (value === "bilirubin") {
    return { color1: require("@/assets/images/exam-colors/bilirubin_3.png") };
  } else if (value === "urobilinogen") {
    return {
      color1: require("@/assets/images/exam-colors/urobilinogen_3.png"),
    };
  } else if (value === "nitrite") {
    return {
      color1: "",
    };
  } else if (value === "sg") {
    return {
      color1: require("@/assets/images/exam-colors/sg_warning_plus.png"),
      color2: require("@/assets/images/exam-colors/sg_warning_minus.png"),
    };
  } else {
    return {
      color1: "",
    };
  }
};

export const dangerStripImg = (value) => {
  if (value === "blood") {
    return {
      color1: require("@/assets/images/exam-colors/blood_4.png"),
      color2: "",
    };
  } else if (value === "glucose") {
    return {
      color1: require("@/assets/images/exam-colors/glucose_5.png"),
      color2: require("@/assets/images/exam-colors/glucose_6.png"),
    };
  } else if (value === "protein") {
    return {
      color1: require("@/assets/images/exam-colors/protein_5.png"),
      color2: require("@/assets/images/exam-colors/protein_6.png"),
    };
  } else if (value === "ketone") {
    return {
      color1: require("@/assets/images/exam-colors/ketone_danger.png"),
      color2: "",
    };
  } else if (value === "leukocytes") {
    return {
      color1: require("@/assets/images/exam-colors/leukocytes_4.png"),
    };
  } else if (value === "bilirubin") {
    return { color1: require("@/assets/images/exam-colors/bilirubin_4.png") };
  } else if (value === "urobilinogen") {
    return {
      color1: require("@/assets/images/exam-colors/urobilinogen_4.png"),
    };
  } else if (value === "nitrite") {
    return {
      color1: require("@/assets/images/exam-colors/nitrite_danger.png"),
    };
  } else if (value === "sg") {
    return {
      color1: require("@/assets/images/exam-colors/sg_4.png"),
    };
  } else {
    return {
      color1: "",
    };
  }
};

export const enterStripImg = (value) => {
  if (value === "ketone") {
    return {
      color1: require("@/assets/images/exam-colors/ketone_enter.png"),
      color2: "",
    };
  }
};
export const exertionStripImg = (value) => {
  if (value === "ketone") {
    return {
      color1: require("@/assets/images/exam-colors/ketone_exertion.png"),
      color2: "",
    };
  }
};
