import i18n from "../../../i18n.js";

export const computeYaxis = (type, value) => {
  switch (type) {
    case "blood":
      if (value === 1) return "0";
      if (value === 2) return "10";
      if (value === 3) return "50";
      if (value === 4) return "250";
      else return "";
    case "protein":
      if (value === 2) return "10";
      else if (value === 3) return "30";
      else if (value === 4) return "100";
      else if (value === 5) return "300";
      else return "";
    case "glucose":
      if (value === 2) return "100";
      else if (value === 3) return "250";
      else if (value === 4) return "500";
      else if (value === 5) return "1000";
      else return "";
    case "ph":
      if (value === 4) return "pH 8";
      else if (value === 5) return "pH 9";
      else return "";
    case "ketone":
      if (value === 1) return "0";
      else if (value === 2) return "50";
      else if (value === 3) return "75";
      else if (value === 4) return "100";
      else if (value === 5) return "50";
      else return "";
  }
};

export const computeKorScore = (type, value) => {
  const scores = ["normal", "caution", "warning", "danger"];

  if (value === 0 || isNaN(value)) return "";

  switch (type) {
    case "blood":
      if (value === 1) return i18n.t("normal");
      else if (value === 2) return i18n.t("caution");
      else if (value === 3) return i18n.t("warning");
      else if (value === 4) return i18n.t("danger");
      else return "";
    case "protein":
      if (value === 1) return i18n.t("normal");
      else if (value === 2) return i18n.t("normal");
      else if (value === 3) return i18n.t("caution");
      else if (value === 4) return i18n.t("warning");
      else if (value === 5 || value === 6) return i18n.t("danger");
      else return "";
    case "glucose":
      if (value === 1 || value === 2) return i18n.t("normal");
      else if (value === 3) return i18n.t("caution");
      else if (value === 4) return i18n.t("warning");
      else if (value === 5 || value === 6) return i18n.t("danger");
      else return "";
    case "ph":
      if (value === 1 || value === 2 || value === 3 || value === 4) return i18n.t("normal");
      else if (value === 5) return i18n.t("warning");
      else return "";
    case "ketone":
      if (value === 1) return i18n.t("normal");
      else if (value === 2) return i18n.t("ketone_caution_level_plus_minus");
      else if (value === 3) return i18n.t("ketone_caution_level_plus");
      else if (value === 4) return i18n.t("warning");
      else if (value === 5) return i18n.t("danger");
      else return "";
    case "leukocytes": {
      return i18n.t(scores[value - 1]);
    }
    case "bilirubin": {
      return i18n.t(scores[value - 1]);
    }
    case "urobilinogen": {
      if (value <= 2) {
        return i18n.t("normal");
      }

      console.warn(value, i18n.t(scores[value - 2]));

      return i18n.t(scores[value - 2]);
    }
    case "nitrite": {
      console.log("init");
      if (value === 1) {
        return i18n.t("normal");
      }

      console.log(i18n.t("danger"));

      return i18n.t("danger");
    }
    case "sg": {
      if (value <= 2) {
        return i18n.t("normal");
      }

      if (value === 3) {
        return i18n.t("caution");
      }

      if (value === 4 || value === 5) {
        return i18n.t("warning");
      }

      return i18n.t("danger");
    }

    default:
      return "";
  }
};

export const computeEnScore = (score, type) => {
  switch (score) {
    case i18n.t("normal"):
      return "normal";
    case i18n.t("ketone_normal"):
      return "ketone_normal";
    case i18n.t("warning"):
      return "warning";
    case i18n.t("caution"):
      return "caution";
    case i18n.t("danger"):
      return "danger";
    case i18n.t("good"):
      return "good";
    case i18n.t("ketone_caution_level_plus_minus"):
      return "caution_plus_minus";
    case i18n.t("ketone_caution_level_plus"):
      return "caution_plus";
    default:
      return "";
  }
};

export const computeColor = (score, type) => {
  switch (score) {
    case i18n.t("normal"):
      return "#00bb00";
    case i18n.t("caution"):
      return "#ffcc00";
    case i18n.t("ketone_caution_level_plus_minus"):
      return "#ffcc00";
    case i18n.t("ketone_caution_level_plus"):
      return "#ffcc00";
    case i18n.t("warning"):
      return "#ff6600";
    case i18n.t("danger"):
      return "#646464";
    case i18n.t("good"):
      return "#41d8e6";
    default:
      return "#646464";
  }
};
