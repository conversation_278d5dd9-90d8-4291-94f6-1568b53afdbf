<template>
  <div>
    <div class="guide-card__wrapper" :class="cardBoxHeight">
      <div class="card-title__wrapper" @click="clickHandler">
        <div class="card-title" :class="isKo ? 'card-title-ko' : 'card-title-en'">
          {{ cardTitle }}
        </div>
        <div class="card-title__ic">
          <img src="@/assets/images/guide-card-arrow.png" loading="lazy" alt="guide-card-arrow" />
        </div>
      </div>
      <div class="card-img__wrapper">
        <img :src="stripImage" alt="" class="strip_img" loading="lazy" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    stripImage: String,
    cardTitle: String,
    cardBoxHeight: String,
  },
  data() {
    return {
      ketoneSmallTxt: "(+)",
      isKo: this.$i18n.locale.includes("ko") || this.$i18n.locale.includes("cn"),
    };
  },

  methods: {
    clickHandler() {
      // const bodyTag = document.getElementsByTagName("body");
      // bodyTag[0].classList.add("noscroll");
      this.$store.commit("openGuideModal");
      // console.log(this.cardTitle);
      console.log(this.cardTitle);
      console.log(this.stripImage);
      this.$store.commit("getCardTitle", this.cardTitle);
    },
  },
};
</script>

<style scoped>
.guide-card__wrapper {
  width: 100%;
  height: 100%;
  background: #ffffff;
  border-radius: 20px;
  position: relative;
  border: 1px solid #ededed;
}

.min-height-140 {
  min-height: 140px;
}

.min-height-80 {
  min-height: 86px;
}

.min-height-140 > .card-title__wrapper {
  display: flex;
  justify-content: space-between;
}

.min-height-80 > .card-title__wrapper {
  display: flex;
  justify-content: space-between;
}

.card-title__wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  padding: 50px 30px;
}

.card-title {
  font-size: 18px;
  text-align: center;
  /* font-family: "Noto Sans KR"; */
  font-style: normal;
  font-weight: 500;
  line-height: 26px;
}

.card-title-ko {
  font-family: "Noto Sans KR";
}

.card-title-en {
  font-family: "GilroyBold";
}

.card-title__ic {
  padding-left: 5px;
}
.card-title__ic > img {
  width: 7px;
}

.card-img__wrapper {
  position: absolute;
  top: 90px;
  right: 0px;
}

.card-img__wrapper > img {
  width: 80%;
  float: right;
}

.card-title__txt--small {
  font-size: 12px;
  font-weight: 600;
}
</style>
