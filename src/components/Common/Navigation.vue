<template>
  <div>
    <!-- <CheckModal
      v-if="analysisWarningModalOpen"
      @cancelHandler="cancelHandler"
      @confirmHandler="confirmHandler"
      :content="this.$i18n.t('do_not_reuse_boat')"
      :leftBtnTxt="this.$i18n.t('no')"
      :rightBtnTxt="this.$i18n.t('yes')"
    /> -->
    <ShopModal
      v-if="isShopShow"
      @handleCloseModalClick="handleCloseModalClick"
    />
    <div class="mobile">
      <div>
        <div class="bottom-nav">
          <div class="bottom-nav__btn" @click="goToHome">
            <div class="btn__icon">
              <v-icon :size="30" id="home-icon" v-if="getPath === '/home'"
                >$home_on</v-icon
              >
              <v-icon :size="30" id="home-icon" v-else>$home_off</v-icon>
              <div class="nav_text">{{ $t("nav_home") }}</div>
            </div>
          </div>

          <!-- <div class="bottom-nav__btn" @click="moveSolution">
            <div class="btn__icon">
              <v-icon :size="30" id="solution-icon" v-if="getPath === '/solution'">$solution_on</v-icon>
              <v-icon :size="30" id="solution-icon" v-else>$solution_off</v-icon>
              <div class="nav_text">{{ $t("nav_solution") }}</div>
            </div>
            <AlertSign :position="position" v-if="isSolution" />
          </div> -->

          <div class="bottom-nav__btn" @click="goToExam">
            <div class="btn__icon">
              <v-icon :size="30">$exam_off</v-icon>
              <div class="nav_text">{{ $t("nav_test") }}</div>
            </div>
          </div>

          <div class="bottom-nav__btn" @click="moveShop">
            <div class="btn__icon">
              <v-icon :size="30" id="shop-icon">$shop_off</v-icon>
              <div class="nav_text">{{ $t("nav_shop") }}</div>
            </div>
            <!-- <AlertSign :position="position" v-if="isShop" /> -->
          </div>

          <div class="bottom-nav__btn" @click="goToMypage">
            <div class="btn__icon">
              <v-icon :size="32" id="my-icon" v-if="getPath === '/mypage'"
                >$my_on</v-icon
              >
              <v-icon :size="32" id="my-icon" v-else>$my_off</v-icon>
              <div class="nav_text">{{ $t("nav_my") }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AlertSign from "@/components/Common/AlertSign.vue";
import CheckModal from "@/components/Common/CheckModal.vue";
import ShopModal from "@/components/Common/ShopModal.ui.vue";

export default {
  name: "Navigation",
  props: {
    path: String,
  },
  components: {
    // AlertSign,
    // CheckModal,
    ShopModal,
  },
  data: () => ({
    isIos:
      navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
      navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    position: "bottom",
    isActive: false,
    showCompleteAlert: false,
    isHome: false,
    isSolution: false,
    isExam: false,
    isShop: false,
    isMypage: false,
    isShopShow: false,
  }),

  computed: {
    getPath() {
      return this.path;
    },
    lastAnalysisTime() {
      return this.$store.state.lastAnalysisTime;
    },
    analysisWarningModalOpen() {
      return this.$store.state.analysisWarningModalOpen;
    },
  },
  updated() {
    setTimeout(() => {
      this.isSolution = false;
      this.isShop = false;
    }, 2000);
  },
  methods: {
    cancelHandler() {
      this.$store.commit("setAnalysisWarningModal", false);
    },

    confirmHandler() {
      this.$router.push({ name: "ExamIntro" });
    },
    isLastAnalysisWithinOneHour() {
      const ONE_HOUR = 60 * 60 * 1000; // 1시간 (밀리초)
      const nowUTC = Date.now(); // 현재 시간 (UTC 밀리초) ex: 1738553415264
      const lastAnalysisUTC = new Date(this.lastAnalysisTime).getTime(); // 저장된 시간 (UTC 밀리초)
      const timeDiff = nowUTC - lastAnalysisUTC;

      return timeDiff < ONE_HOUR;
    },
    moveSolution() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("savedFoodList");
      sessionStorage.removeItem("savedBookmarkDataPage");
      sessionStorage.removeItem("scrollPosition");
      sessionStorage.removeItem("solutionCurTab");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedDataPage");
      // document.body.classList.remove("no-scroll");
      this.isSolution = true;
      // this.$router.push({ path: "/solution" });
    },
    moveShop() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("savedFoodList");
      sessionStorage.removeItem("savedBookmarkDataPage");
      sessionStorage.removeItem("scrollPosition");
      sessionStorage.removeItem("solutionCurTab");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedDataPage");
      this.isShop = true;

      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

      if (timezone.includes("Seoul")) {
        this.isShopShow = true;
        return;
      }

      const message =
        "https://www.indiegogo.com/projects/cym702-pet-easy-at-home-pet-health-test-kits/reft/38410017/cym702petapp";
      /* global Webview */
      /* eslint-disable no-undef */
      Webview.openUrl(message);
    },
    showAlert() {
      this.showCompleteAlert = true;
    },
    closeAlert(fromChild) {
      this.showCompleteAlert = fromChild;
    },
    goToHome() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("savedFoodList");
      sessionStorage.removeItem("savedBookmarkDataPage");
      sessionStorage.removeItem("scrollPosition");
      sessionStorage.removeItem("solutionCurTab");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedDataPage");
      // document.body.classList.remove("no-scroll");
      this.$router.push({ name: "Home" });
    },
    goToExam() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("savedFoodList");
      sessionStorage.removeItem("savedBookmarkDataPage");
      sessionStorage.removeItem("scrollPosition");
      sessionStorage.removeItem("solutionCurTab");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedDataPage");
      // document.body.classList.remove("no-scroll");

      // if (this.isLastAnalysisWithinOneHour()) {
      //   this.$store.commit("setAnalysisWarningModal", true);
      //   return;
      // }

      console.log(this.isLastAnalysisWithinOneHour());
      this.$router.push({ name: "ExamIntro" });
    },
    goToMypage() {
      sessionStorage.removeItem("savedBookmarkList");
      sessionStorage.removeItem("savedFoodList");
      sessionStorage.removeItem("savedBookmarkDataPage");
      sessionStorage.removeItem("scrollPosition");
      sessionStorage.removeItem("solutionCurTab");
      sessionStorage.removeItem("scrollbookmarkPosition");
      sessionStorage.removeItem("savedDataPage");
      // document.body.classList.remove("no-scroll");
      this.$router.push({ name: "Mypage" });
    },
    handleCloseModalClick() {
      this.isShopShow = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.bottom-nav {
  max-width: 450px;
  width: 100%;
  position: fixed;
  bottom: -0.5px;
  display: flex;
  padding: 5px 10px 25px 10px;
  background-color: #fff;
  border-top: 0.5px solid #ededed;
  z-index: 9999;
}

::v-deep .v-icon {
  padding-left: 3px !important;
  padding-top: 15px !important;
}

.bottom-nav__btn {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  margin: 0 auto;
}

.btn__icon {
  transition: 0.3s;
  width: 100%;
  // padding-left: 20px;
}

.nav_text {
  font-size: 12px;
  color: #646464;
  font-weight: 500;
  letter-spacing: -0.03em;
  width: 100%;
  display: flex;
  justify-content: center;
}

@media screen and (max-width: 375px) {
  .nav_text {
    font-size: 11px;
  }
}

#home-icon {
  padding-left: 5px !important;
}

#solution-icon {
  padding-left: 7px !important;
}

#shop-icon {
  padding-left: 5px !important;
}

#my-icon {
  padding-left: 6px !important;
}
</style>
