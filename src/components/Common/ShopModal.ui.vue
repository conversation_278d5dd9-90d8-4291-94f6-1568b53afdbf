<template>
  <div class="modal-background">
    <div class="modal-container" :class="{ closing: isClosing }">
      <header class="image-container" @click="handleCloseModalClick">
        <img
          src="@/assets/images_assets/icons/close-btn-solid-ic.png"
          alt="close-icon"
          loading="lazy"
          width="20px"
          height="20px"
          class="close-icon"
        />
      </header>
      <div class="upper-container">
        <img
          src="@/assets/images/shop-image.png"
          alt="shop-image"
          loading="lazy"
          class="img"
        />
        <span class="shop-text"
          >Cym<sup>702</sup> Pet: 집에서 쉽고 빠르고 정확하게 우리 아이 건강을
          확인하세요!</span
        >
        <button class="btn" @click="handleOpenShopClick(10)">
          10항목 키트 사러가기
        </button>
        <button class="btn" @click="handleOpenShopClick(5)">
          5항목 키트 사러가기
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ShopModal",

  data() {
    return {
      isClosing: false,
    };
  },

  methods: {
    handleCloseModalClick() {
      this.isClosing = true;
      setTimeout(() => {
        this.$emit("handleCloseModalClick");
      }, 400);
    },

    handleOpenShopClick(kitCount = 5) {
      console.log(kitCount);
      /* global Webview */
      /* eslint no-undef: "error" */
      const shopUrl = {
        5: "https://mkt.shopping.naver.com/link/681ac54bab37d362524327b1?nt_source=cym702pet&nt_medium=app&nt_detail=mobile&nt_keyword=kit",
        10: "https://www.indiegogo.com/projects/cym702-pet-easy-at-home-pet-health-test-kits/reft/38410017/cym702petapp",
      };
      const url = shopUrl[kitCount] ?? "";

      if (!url) {
        throw new Error("url is not defined");
      }

      Webview.openUrl(shopUrl[kitCount]);
    },
  },
};
</script>

<style lang="scss" scoped>
.modal-background {
  width: 100vw;
  max-width: 450px;
  height: 100vh;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  background-color: rgba(0, 0, 0, 0.6);
}

.modal-container {
  width: 100%;
  height: 345px;
  background-color: #ffffff;
  border-radius: 30px 30px 0 0;
  padding: 30px 8.33%;
  animation: slide-up 0.4s ease-out forwards;
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slide-down {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

.modal-container.closing {
  animation: slide-down 0.4s ease-out forwards;
}

.btn {
  height: 50px;
  position: relative;
  max-width: 340px;
  width: 100%;
  border-radius: 10px;
  line-height: 50px;
  font-size: 20px;
  font-weight: bold;
  background-color: #41d8e6;
  color: #ffffff;
}

.shop-text {
  font-size: 16px;
  font-weight: 400;
  line-height: 23.17px;
  letter-spacing: -0.02em;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.img {
  width: 278px;
  height: auto;
}

.image-container {
  width: 100%;
  height: 20px;
  position: relative;
  display: flex;
  justify-content: flex-end;
  z-index: 3;
}

.upper-container {
  height: calc(100% - 20px);
  transform: translateY(-35%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1;
  gap: 15px;
}
</style>
