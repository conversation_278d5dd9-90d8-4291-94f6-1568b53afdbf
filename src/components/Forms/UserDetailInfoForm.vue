<template>
  <div class="basic-data__wrapper">
    <section>
      <PetTypeForm @petTypeHandler="petTypeHandler" />
    </section>

    <section>
      <PetSearchForm :typeList="typeList" @breedHandler="breedHandler" />
    </section>

    <section>
      <GenderForm @genderInputHandler="genderInputHandler" />
    </section>

    <section>
      <NeuteredForm
        :initialNeutered="neutered"
        @neuteredHandler="neuteredHandler"
      />
    </section>

    <section>
      <NameForm @nameInputHandler="nameInputHandler" />
    </section>

    <section>
      <WeightForm @weightInputHandler="weightInputHandler" />
    </section>

    <section>
      <WeightGoalForm @weightGoalInputHandler="weightGoalInputHandler" />
    </section>

    <section>
      <BirthForm @birthInputHandler="birthInputHandler" />
    </section>

    <section>
      <AdoptionDateForm
        :initialBirth="initialBirth"
        @adoptionDateHandler="adoptionDateHandler"
        @sameBirthHandler="sameBirthHandler"
      />
    </section>

    <section class="last">
      <RegistrationNumberForm
        @registrationNumberHandler="registrationNumberHandler"
      />
    </section>

    <div class="btn__wrapper">
      <v-btn
        color="#41D8E6"
        class="main-large-btn"
        @click="nextPhaseHandler"
        :disabled="!infoValid"
        elevation="0"
      >
        {{ $t("next_btn") }}
      </v-btn>
    </div>
  </div>
</template>

<script>
import API from "@/api/auth/index.js";

import PetTypeForm from "./PetTypeForm.vue";
import PetSearchForm from "./PetSearchForm.vue";
import GenderForm from "./GenderForm.vue";
import NeuteredForm from "./NeuteredForm.vue";
import NameForm from "./NameForm.vue";
import WeightForm from "./WeightForm.vue";
import WeightGoalForm from "./WeightGoalForm.vue";
import BirthForm from "./BirthForm.vue";
import AdoptionDateForm from "./AdoptionDateForm.vue";
import RegistrationNumberForm from "./RegistrationNumberForm.vue";

export default {
  name: "UserDetailInfoForm",
  components: {
    PetTypeForm,
    PetSearchForm,
    GenderForm,
    NeuteredForm,
    NameForm,
    WeightForm,
    WeightGoalForm,
    BirthForm,
    AdoptionDateForm,
    RegistrationNumberForm,
  },
  data() {
    return {
      typeList: [],
      subjectTypeId: null,
      gender: null,
      neutered: null,
      name: "",
      nameValid: false,
      initialWeight: null,
      initialWeightValid: false,
      targetWeight: null,
      targetWeightValid: true,
      birth: null,
      birthValid: false,
      initialBirth: null,
      adoptionDate: null,
      adoptionDateValid: true,
      registrationNumber: null,
      registrationNumberValid: true,
    };
  },

  computed: {
    infoValid() {
      const requiredFieldsValid =
        this.gender !== null &&
        this.neutered !== null &&
        this.nameValid &&
        this.initialWeightValid &&
        this.birthValid;

      const optionalFieldsValid =
        (!this.adoptionDate || this.adoptionDateValid) &&
        (!this.targetWeight || this.targetWeightValid) &&
        (!this.registrationNumber || this.registrationNumberValid);

      return (
        requiredFieldsValid &&
        optionalFieldsValid &&
        this.subjectTypeId !== null
      );
    },
  },

  methods: {
    nextPhaseHandler() {
      const petInfo = {
        typeId: this.subjectTypeId,
        sex: this.gender,
        neutered: this.neutered,
        nickname: this.name,
        initialWeight: Number(this.initialWeight),
        targetWeight: Number(this.targetWeight),
        birth: this.birth,
        adoptionDate: this.adoptionDate,
        registrationNumber: this.registrationNumber,
      };
      console.log(petInfo);
      this.$store.commit("GET_PET_DETAIL", petInfo);
      this.$emit("nextPhaseHandler", 4);
    },

    // emit
    petTypeHandler(type) {
      console.log(type);
      this.getTypeList(type);
    },
    breedHandler(breedObj) {
      console.log(breedObj);
      this.subjectTypeId = Number(breedObj.id);
    },

    genderInputHandler(gender) {
      // console.log(gender);
      this.gender = gender;
    },

    neuteredHandler(neutered) {
      // console.log(neutered);
      this.neutered = neutered;
    },

    nameInputHandler(inputName) {
      // console.log(inputName);
      this.name = inputName.name;
      this.nameValid = inputName.valid;
    },
    birthInputHandler(inputBirth) {
      // console.log(inputBirth);
      this.birth = inputBirth.birth;
      this.birthValid = inputBirth.valid;
    },
    adoptionDateHandler(adoptionDateObj) {
      console.log(adoptionDateObj);
      this.adoptionDate = adoptionDateObj.adoptionDate;
      this.adoptionDateValid =
        adoptionDateObj.valid === null ? true : adoptionDateObj.valid;
    },
    sameBirthHandler(isSame) {
      this.initialBirth = isSame ? this.birth : null;
    },

    weightInputHandler(inputWeight) {
      // console.log(inputWeight);
      this.initialWeight = inputWeight.weight;
      this.initialWeightValid = inputWeight.valid;
    },
    weightGoalInputHandler(inputGoalWeight) {
      // console.log(inputGoalWeight);
      // console.log(inputGoalWeight.goal);
      this.targetWeight = inputGoalWeight.goal;
      this.targetWeightValid =
        inputGoalWeight.goal === null ? true : inputGoalWeight.valid;
    },
    registrationNumberHandler(registrationNumberObj) {
      // console.log(registrationNumberObj);
      this.registrationNumber = registrationNumberObj.registrationNumber;
      this.registrationNumberValid =
        registrationNumberObj.valid === null
          ? true
          : registrationNumberObj.valid;
    },

    // api
    async getTypeList(type) {
      try {
        const { data, status } = await API.getTypeList(type);
        // console.log(data, status);
        if (status == 200) {
          let petList = data.subjectTypes;

          if (!this.isKo) {
            petList.sort((a, b) => {
              return a.english.localeCompare(b.english);
            });

            petList = petList.map((item) => {
              return {
                ...item,
                english: `${item.english
                  .charAt(0)
                  .toUpperCase()}${item.english.slice(1)}`,
              };
            });
          }

          this.typeList = petList;
        }
      } catch (e) {
        console.error(e);
      }
    },
  },
  mounted() {
    // console.log("name valid check this");
    // console.log(this.name);
    // console.log(this.nameValid);
    const basicDataWrapper = this.$el.querySelector(".basic-data__wrapper");

    if (basicDataWrapper) {
      basicDataWrapper.style.overflowY = "scroll";
    }
  },
};
</script>

<style lang="scss" scoped>
@media (prefers-color-scheme: dark) {
  body {
    scrollbar-color: #555 rgba(255, 255, 255, 0.05); /* thumb, track */
    scrollbar-width: thin;
  }
}
.basic-data__wrapper {
  width: 100%;
  padding: 20px 30px 50px;
  height: 60vh;
  overflow-y: scroll;

  -webkit-overflow-scrolling: touch; /* iOS 부드러운 스크롤 */

  /* 스크롤바 공통 디자인 (Webkit 기반 브라우저용) */
  &::-webkit-scrollbar {
    width: 10px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #bcbcbc; /* 중간 회색 */
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: padding-box;
  }

  /* Firefox용 */
  scrollbar-width: thin;
  scrollbar-color: #bcbcbc transparent;
}
section {
  margin: 20px 0 30px;
  width: 100%;
}

.last {
  margin-bottom: 80px;
}

.gender-checkbox__items {
  display: flex;
  width: 100%;
}

.gender-checkbox__item {
  width: 50%;
}
.gender-input__title {
  width: 100%;
  text-align: left;
  font-size: 16px;
}

.age-input__subtitle {
  font-size: 12px;
  text-align: left;
  color: #a7a7a7;
}

.age-input__items {
  display: flex;
  padding-top: 15px;
}

.age {
  padding: 0 12px;
}
.text-field-label {
  padding-bottom: 14px;
}

.age-input__title {
  text-align: left;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
  transition: 0.1s ease-in-out;
}

.btn__wrapper {
  position: fixed;
  bottom: 30px;
  left: 0;
  width: 100%;
  padding: 0 30px;
  margin: auto;
}

.main-large-btn {
  width: 100%;
  min-height: 45px !important;
  max-height: 50px !important;
  height: calc(100vh / 15) !important;
  border-radius: 10px;
  color: #fff !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  letter-spacing: -0.03em;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

::v-deep .theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  color: #c9f4f8 !important;
  opacity: 1;
}
</style>
