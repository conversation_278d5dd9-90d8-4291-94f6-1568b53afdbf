<template>
  <div class="weight-form">
    <v-text-field
      color="#41d8e6"
      v-model="weight"
      :placeholder="this.$i18n.t('profile_description')"
      :persistent-placeholder="true"
      type="number"
      inputmode="numeric"
      :error-messages="weightError"
    >
      <template v-slot:label>
        <div class="text-field-label">{{ $t("target_weight") }}</div>
      </template>
    </v-text-field>
  </div>
</template>

<script>
export default {
  props: {
    initialWeight: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      weight: null,
      validWeight: false,
      weightError: "",
    };
  },
  methods: {
    weightValidation(weight) {
      if (weight === null || weight === undefined) return false;
      const numWeight = Number(weight);
      return numWeight > 0 && numWeight < 201;
    },
  },
  watch: {
    weight(newVal) {
      if (this.weight === null || this.weight === undefined) {
        this.weightError = "";
        this.validWeight = false;
      } else if (this.weightValidation(newVal)) {
        this.weightError = "";
        this.validWeight = true;
      } else {
        this.weightError = this.$i18n.t("invalid");
        this.validWeight = false;
      }

      const weight = { goal: this.weight, valid: this.validWeight };
      this.$emit("weightGoalInputHandler", weight);
    },
  },
  mounted() {
    this.weight = this.initialWeight;
  },
};
</script>

<style lang="scss" scoped>
.weight-form {
  width: 100%;
}

::v-deep v-text-field input {
  font-size: 18px;
}
.text-field-label {
  font-size: 18px;
  font-weight: 500;
  line-height: normal;
}

::v-deep .v-input {
  font-family: GilroyMedium !important;
  margin-bottom: 20px;
  font-size: 20px;
  letter-spacing: -0.03em;
}

::v-deep .v-text-field input {
  padding: 0;
  font-size: 20px;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 14px !important;
    letter-spacing: -0.03em;
    padding: 0;
    font-weight: 400 !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
