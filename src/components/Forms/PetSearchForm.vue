<template>
  <div>
    <div class="breed__wrapper">
      <div class="breed-title">{{ $t("breed") }}</div>
      <div class="search-input">
        <div><img src="@/assets/images_assets/icons/search-ic.png" /></div>
        <!-- <input
          @click="modalHandler"
          v-model="selectedBreedId"
          class="invisible-cursor"
          :placeholder="this.$i18n.t('search_breed')"
        /> -->
        <div class="breedButton" @click="modalHandler">
          {{ selectedBreedId }}
        </div>
      </div>
    </div>
    <div v-if="isOpenModal" class="form-modal">
      <div class="close-btn__wrapper">
        <div @click="modalHandler" class="close-btn">{{ $t("exit_btn") }}</div>
      </div>
      <div class="search-input input__wrapper">
        <img src="@/assets/images_assets/icons/search-ic.png" />
        <input @input="handleInput" v-model="searchInput" />
        <img
          @click="inputRemoveHandler"
          id="search-remove-btn"
          src="@/assets/images_assets/icons/gray-x-circle.png"
        />
      </div>
      <div class="result__wrapper">
        <div
          class="result"
          @click="typeHandler(breed)"
          v-for="breed in filteredList"
          :key="breed.id"
        >
          {{
            isKo
              ? trimText(breed.korean)
              : isCn
              ? trimText(breed.chinese)
              : trimText(breed.english)
          }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { debounce } from "lodash";

export default {
  name: "PetSearchFrom",
  props: {
    typeList: Array,
    initialBreed: String,
  },
  data() {
    return {
      isKo: false,
      isCn: false,
      filteredList: [],
      searchInput: "",
      selectedBreedId: null,
      isOpenModal: false,
    };
  },
  computed: {
    filteredItems() {
      const term = this.trimText(this.searchInput);
      const chineseCharRegex = /[\u4e00-\u9fa5]/;
      const pinyinRegex = /^[a-zA-Z]+$/;
      const pinyin = [];
      const chineseChars = [];

      if (term === "") {
        return this.typeList;
      }

      if (this.isCn) {
        const words = term.split("");

        words.forEach((word) => {
          if (chineseCharRegex.test(word)) {
            chineseChars.push(word);
          } else if (pinyinRegex.test(word)) {
            pinyin.push(word.toLowerCase());
          }
        });

        const result = chineseChars.join("");

        return this.typeList.filter((item) => {
          return (
            item.chinese.includes(result) ||
            item.chinese.includes(term) ||
            item.english.toLowerCase().includes(term) ||
            item.korean.toLowerCase().includes(term)
          );
        });
      }

      return this.typeList.filter((item) => {
        return (
          item.korean.toLowerCase().includes(term.toLowerCase()) ||
          item.english.toLowerCase().includes(term.toLowerCase())
        );
      });
    },
  },
  watch: {
    typeList(newVal) {
      console.log(newVal);
      const petList = newVal;

      this.filteredList = petList;
    },
  },
  methods: {
    trimText(str) {
      return str.trim();
    },
    inputRemoveHandler() {
      this.searchInput = "";
      this.filteredList = this.filteredItems;
    },
    modalHandler() {
      this.isOpenModal = this.isOpenModal ? false : true;
    },
    typeHandler(breedObj) {
      console.log(breedObj);
      this.selectedBreedId = this.isKo
        ? this.trimText(breedObj.korean)
        : this.isCn
        ? this.trimText(breedObj.chinese)
        : this.trimText(breedObj.english);
      this.isOpenModal = false;
      this.$emit("breedHandler", breedObj);
    },
    handleInput: debounce(function() {
      this.filteredList = this.filteredItems;
    }, 300),
  },
  mounted() {
    this.isKo = this.$i18n.locale.includes("ko");
    this.isCn = this.isKo === false && this.$i18n.locale.includes("cn");

    console.log(this.isKo, this.isCn);
    this.selectedBreedId =
      this.initialBreed !== null || this.initialBreed !== undefined
        ? this.initialBreed
        : this.$i18n.t("search_breed");
  },
};
</script>

<style lang="scss" scoped>
.breed__wrapper {
  width: 100%;
  height: 100px;
  text-align: left;
}
.breed-title {
  color: #646464;
  font-size: 18px;
  font-weight: 500;
  margin: 0 0 15px;
}

.search-input {
  width: 100%;
  height: 40px;
  display: flex;
  gap: 5px;
  background: #ffffff;
  border-radius: 10px;
  text-align: left;
  padding: 0px 11px;
  position: relative;
  top: 0;
  line-height: 40px;
  border: 1px solid #a7a7a7;

  img {
    width: 24px;
    vertical-align: sub;
    // position: absolute;
    // top: 50%;
    // transform: translateY(-50%);
  }

  input {
    margin-left: 30px;
    outline: none;
    position: absolute;
    top: -2px;
    width: 80%;
  }
  &:hover {
    border: 2px solid #41d8e6;
    // padding: 2px 22px;
  }
}

.breedButton {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.form-modal {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 85vh;
  position: fixed;
  padding: 30px;
  background: #ededed;
  box-shadow: 0px 0px 10px 4px rgba(0, 0, 0, 0.35);
  border-radius: 30px 30px 0px 0px;
  z-index: 999;
}

.close-btn__wrapper {
  width: 100%;
  display: flex;
  justify-content: end;
  color: #ffffff;
}
.close-btn {
  width: 55px;
  height: 25px;
  font-weight: 700;
  font-size: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #c8c8c8;
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
  border-radius: 5px;
}

.input__wrapper {
  margin: 30px 0;
  img {
    width: 24px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
}

#search-remove-btn {
  width: 20px;
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
}

.result__wrapper {
  width: 100%;
  height: 85%;
  padding-bottom: 10px;
  overflow: scroll;
}

.result {
  font-size: 18px;
  text-align: left;
  margin-bottom: 20px;
}
</style>
