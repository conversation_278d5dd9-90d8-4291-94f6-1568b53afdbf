<template>
  <div class="birth-form__container">
    <div class="age-input__title">{{ $t("adoption_date") }}</div>
    <div class="userdetail-text-field__items">
      <div class="name-input__item">
        <v-text-field
          v-model="year"
          type="number"
          inputmode="numeric"
          color="#41d8e6"
          :placeholder="this.$i18n.t('birth_year_placeholder')"
          :error-messages="yearError"
          @focus="focusInHandler"
          @blur="focusOutHandler"
        >
        </v-text-field>
      </div>
      <div class="name-input__item age">
        <v-text-field
          type="number"
          inputmode="numeric"
          v-model="month"
          color="#41d8e6"
          :placeholder="this.$i18n.t('birth_month_placeholder')"
          :error-messages="monthError"
          @focus="focusInHandler"
          @blur="focusOutHandler"
        >
        </v-text-field>
      </div>
      <div class="name-input__item">
        <v-text-field
          type="number"
          inputmode="numeric"
          v-model="day"
          color="#41d8e6"
          :placeholder="this.$i18n.t('birth_day_placeholder')"
          :error-messages="dayError"
          @focus="focusInHandler"
          @blur="focusOutHandler"
        >
        </v-text-field>
      </div>
    </div>
    <v-checkbox
      color="#41d8e6"
      off-icon="$check_box"
      on-icon="$check_box_inactive"
      v-model="sameBirth"
      @change="sameBirthCheckBoxHandler"
    >
      <template v-slot:label>
        <div class="gender-text">{{ $t("same_birth") }}</div>
      </template>
    </v-checkbox>
  </div>
</template>

<script>
export default {
  props: {
    initialBirth: String,
  },
  data() {
    return {
      year: null,
      month: null,
      day: null,
      yearError: "",
      monthError: "",
      dayError: "",
      validDate: false,
      sameBirth: false,
    };
  },
  methods: {
    focusInHandler() {
      const ageInputTitle = document.querySelector(".age-input__title");
      ageInputTitle.style.color = "#41D8E6";
    },
    focusOutHandler() {
      const ageInputTitle = document.querySelector(".age-input__title");
      ageInputTitle.style.color = "rgba(0, 0, 0, 0.6)";
    },
    yearValidation(year) {
      const today = new Date();
      const yearNow = today.getFullYear();
      if (1900 > year || year > yearNow || this.year === null) return false;
      else return true;
    },
    monthValidation(month) {
      if (month < 1 || month > 12 || month.length === 1 || this.month === null) return false;
      else return true;
    },
    dayValidation(day) {
      if (day < 1 || day > 31 || day.length === 1 || this.day === null) return false;
      else if (
        (Number(this.month) === 4 ||
          Number(this.month) === 6 ||
          Number(this.month) === 9 ||
          Number(this.month) === 11) &&
        Number(day) === 31
      )
        return false;
      else if (Number(this.month) === 2) {
        // 2월 29일(윤년) 체크
        const isleap = Number(this.year) % 4 === 0 && (Number(this.year) % 100 !== 0 || Number(this.year) % 400 === 0);
        // console.log(isleap);
        if (Number(day) > 29 || (Number(day) === 29 && !isleap)) {
          return false;
        } else {
          return true;
        }
      } else return true;
    },
    maxLengthCheck(object) {
      if (object.value.length > object.maxLength) {
        object.value = object.value.slice(0, object.maxLength);
      }
    },
    sameBirthCheckBoxHandler(newVal) {
      this.$emit("sameBirthHandler", newVal);
    },
    setInitialBirth() {
      if (this.initialBirth === undefined || this.initialBirth === null) {
        this.year = null;
        this.month = null;
        this.day = null;
      } else {
        const [y, m, d] = this.initialBirth.split("-");
        // console.log(this.initialBirth);
        this.year = y || 0;
        this.month = m || 0;
        this.day = d || 0;
      }
    },
  },
  watch: {
    year(newVal) {
      // console.log("is here?");
      if (this.yearValidation(newVal)) {
        this.yearError = "";
      } else {
        this.yearError = this.$i18n.t("invalid");
      }
      this.yearValidation(this.year) && this.monthValidation(this.month) && this.dayValidation(this.day)
        ? (this.validDate = true)
        : (this.validDate = false);
      const date = { adoptionDate: `${this.year}-${this.month}-${this.day}`, valid: this.validDate };
      
      this.$emit("adoptionDateHandler", date);
    },
    month(newVal) {
      if (this.monthValidation(newVal)) this.monthError = "";
      else this.monthError = this.$i18n.t("invalid");
      this.yearValidation(this.year) && this.monthValidation(this.month) && this.dayValidation(this.day)
        ? (this.validDate = true)
        : (this.validDate = false);
      const date = { adoptionDate: `${this.year}-${this.month}-${this.day}`, valid: this.validDate };
      this.$emit("adoptionDateHandler", date);
    },
    day(newVal) {
      console.log(newVal);
      if (this.dayValidation(newVal)) this.dayError = "";
      else this.dayError = this.$i18n.t("invalid");
      this.yearValidation(this.year) && this.monthValidation(this.month) && this.dayValidation(this.day)
        ? (this.validDate = true)
        : (this.validDate = false);
      const date = { adoptionDate: `${this.year}-${this.month}-${this.day}`, valid: this.validDate };
      this.$emit("adoptionDateHandler", date);
    },
    initialBirth(newVal) {
      // console.log(newVal);
      this.setInitialBirth();
    },
  },
  mounted() {
    console.log(this.initialBirth);
    this.setInitialBirth();
  },
};
</script>

<style lang="scss" scoped>
.birth-form__container {
  width: 100%;
  // height: 135px;
}

.userdetail-text-field__items {
  display: flex;
}
.age-input__subtitle {
  font-size: 14px;
  text-align: left;
  color: #a7a7a7;
  letter-spacing: -0.03em;
  padding-bottom: 10px;
}

.age-input__items {
  display: flex;
  padding-top: 15px;
}

.age {
  padding: 0 12px;
}
.text-field-label {
  padding-bottom: 14px;
}

.age-input__title {
  text-align: left;
  font-size: 18px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
  transition: 0.1s ease-in-out;
}

.gender-text {
  color: #646464;
  font-size: 18px;
  // font-family: "Noto Sans KR";
}

::v-deep .v-text-field input {
  padding: 0;
  font-size: 20px;
}
::v-deep .v-input {
  font-family: GilroyMedium !important;
  // margin-bottom: 20px;
  font-size: 20px;
}
::v-deep .v-text-field {
  padding-top: 5px;
}
::v-deep .v-input input {
  max-height: 25px;
}
::v-deep v-text-field input {
  font-size: 18px;
}
::v-deep .v-text-field .v-label {
  top: -20px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 14px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
::v-deep .v-input--selection-controls {
  margin-top: 10px;
}

::v-deep .v-input--selection-controls__input {
  padding-top: 5px !important;
}

::v-deep .v-input--selection-controls__input {
  margin: 0 !important;
}

::v-deep .theme--light.v-messages {
  display: none;
}

::v-deep .v-text-field .v-label {
  top: -30px !important;
  font-weight: 500;
}
::v-deep .v-text-field__slot {
  input::placeholder {
    font-size: 12px !important;
  }
}
::v-deep .v-input .v-label {
  line-height: 15px !important;
}
::v-deep .v-label--active {
  transform: translateY(0px) !important;
}
</style>
