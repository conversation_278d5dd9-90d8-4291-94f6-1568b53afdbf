import authMutations from "./auth";
import joinMutation from "./join";
import cym702 from "./cym702.js";

export default {
  ...authMutations,
  ...joinMutation,
  ...cym702,

  setLoginError(state, status) {
    state.loginError = status;
  },
  IMAGEUPLOAD(state, image) {
    state.userImage = image;
  },
  SHOWGUIDE(state) {
    state.showCymscore = true;
  },

  setHistoryTabChanged(state, status) {
    state.historyTabChanged = status;
  },

  CLOSEGUIDE(state) {
    state.showCymscore = false;
  },

  EMAILSIGN(state) {
    state.alreadySigned = true;
  },

  setCurPw(state, pw) {
    state.password = pw;
  },

  closeAlert(state) {
    state.alreadySigned = false;
  },

  completeAlert(state) {
    state.showCompleteModal = true;
  },

  setIsBoat(state, status) {
    state.isBoat = status;
  },

  closeOverlay<PERSON>andler(state) {
    state.isCompleted = false;
  },

  setShowExamLoading(state, status) {
    state.showExamLoading = status;
  },
  setShowExamErrorModal(state, status) {
    state.showExamErrorModal = status;
  },

  bluetoothSuccess(state) {
    state.isSuccess = true;
  },

  openKetoneEditModal(state, status) {
    state.openKetoneEditModal = status;
  },

  openGuideModal(state) {
    state.openGuide = true;
  },

  closeGuideModal(state) {
    state.openGuide = false;
  },

  getCardTitle(state, title) {
    state.cardTitle = title;
  },

  enableTouch(state) {
    state.isEnableTouch = false;
  },

  disableTouch(state) {
    state.isEnableTouch = true;
  },

  openSelectModal(state) {
    state.showSelectModal = true;
  },

  closeSelectModal(state) {
    state.showSelectModal = false;
  },
  showSelectBtn(state) {
    state.showSelectBtn = true;
  },
  hideSelectBtn(state) {
    state.showSelectBtn = false;
  },
  getFoodCard(state, foodInfo) {
    state.selectedFood = foodInfo;
  },
  selectedFoodId(state, foodId) {
    state.selectedFoodId = foodId;
  },
  clickTitle(state, isClicked) {
    state.clickTitle = isClicked;
  },
  selectionBtn(state, status) {
    state.selectionBtn = status;
  },
  recordSelectBtn(state, status) {
    state.recordSelectBtn = status;
  },
  showRecordSelectModal(state, status) {
    state.showRecordSelectModal = status;
  },
  recordSelectType(state, status) {
    state.recordSelectType = status;
  },
  filterBtn(state, status) {
    state.filterBtn = status;
  },
  setAmountRatio(state, percentage) {
    state.FoodAmountPercentage = [...state.FoodAmountPercentage, percentage];
  },
  getUsername(state, username) {
    state.username = username;
  },
  GET_CUR_WEIGHT(state, weight) {
    state.weight = weight;
  },
  getUserPhone(state, phone) {
    state.phone = phone;
  },
  getUserImage(state, img) {
    state.userImg = img;
  },
  setSelectUser(state, id) {
    state.selectUser = id;
  },
  setSubUserId(state, id) {
    state.subUserId = id;
  },
  setSubUser(state, name) {
    state.subUser = name;
  },
  setSubUserGender(state, gender) {
    state.subUserGender = gender;
  },
  setSubUserBirth(state, birth) {
    state.subUserBirth = birth;
  },
  setSubUserType(state, type) {
    state.subUserType = type;
  },
  setNoSubjects(state, status) {
    state.noSubjects = status;
  },
  setAnalysisWarningModal(state, status) {
    state.analysisWarningModalOpen = status;
  },
  setLastAnalysisTime(state, createdAt) {
    state.lastAnalysisTime = createdAt;
  },
};
