export default {
  GET_RECENT_CYMDATA(state, data) {
    state.cymRecentData = data;
  },
  GET_TOTAL_SCORE(state, score) {
    state.totalScore = score;
  },
  GET_TARGET_WATER(state, target) {
    state.targetWater = target;
  },
  GET_CHART_DATA(state, chartData) {
    state.chartData = chartData;
  },
  // GET_BLOOD_DATA(state, blood) {
  //   state.urineTestResult.blood = blood;
  // },
  // GET_GLUCOSE_DATA(state, glucose) {
  //   state.urineTestResult.glucose = glucose;
  // },
  // GET_PROTEIN_DATA(state, protein) {
  //   state.urineTestResult.protein = protein;
  // },
  // GET_PH_DATA(state, ph) {
  //   state.urineTestResult.ph = ph;
  // },
  // GET_KETONE_DATA(state, ketone) {
  //   state.urineTestResult.ketone = ketone;
  // },
  SET_TAB_STATE(state, clicked) {
    state.tabChange = clicked;
  },
  GET_ACTIVE_DROPLET_IDX(state, idx) {
    state.activeDropletIdx = idx;
  },
  GET_MAIN_CHART_IDX(state, idx) {
    state.mainchartIdx = idx;
  },
  CLOSE_COMPLETE_MODAL(state) {
    state.showCompleteModal = false;
  },
  OPEN_COMPLETE_MODAL(state) {
    state.showCompleteModal = false;
  },
  SETUP_COMPLETE_MODAL(state) {
    state.showCompleteModal = null;
  },
  SET_HISTORY_PAGE(state, page) {
    state.historyPage = page;
  },
  SET_HISTORY_TOTAL_PAGE(state, total) {
    state.historyTotalPage = total;
  },
  SET_HISTORY_LIMIT(state, limit) {
    state.historyLimit = limit;
  },
  SET_TOOLTIP_INDEX(state, index) {
    state.tooltipIndex = index;
  },
  GET_CYM_HISTORY(state, cymHistory) {
    state.urineTestResult.blood = cymHistory.blood;
    state.urineTestResult.glucose = cymHistory.glucose;
    state.urineTestResult.protein = cymHistory.protein;
    state.urineTestResult.ph = cymHistory.ph;
    state.urineTestResult.ketone = cymHistory.ketone;
  },
  GET_CYM_AVG(state, avg) {
    state.cymHistory.cymAvg = avg;
  },
  GET_CYM_SCORE(state, score) {
    state.cymHistory.cymScore = score;
  },
  GET_MONTH(state, month) {
    state.month = month;
  },
  GET_WEIGHT_DATA(state, weightData) {
    state.careHistory.weightData = weightData;
  },
  GET_WATER_DATA(state, waterData) {
    state.careHistory.waterData = waterData;
  },
  GET_CAREDETAIL_DATA(state, careDetailData) {
    state.careHistory.careDetailData = careDetailData;
  },
  GET_PEE_DATA(state, peeData) {
    state.careHistory.peeData = peeData;
  },
  GET_NO_DATA_LOCK_MODAL(state, noDataLockModalState) {
    state.noDataLockModalState = noDataLockModalState;
  },
};
