import Vue from "vue";
import Vuex from "vuex";
import actions from "./actions/index.js";
import mutations from "./mutations/index.js";

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    accessToken: localStorage.getItem("auth") || "",
    analysisWarningModalOpen: false,
    lastAnalysisTime: null,
    isLogin: false,
    loginError: false,
    password: "",
    historyTabChanged: "",
    tabChange: false,

    // join
    join: {
      account: "",
      password: "",
      marketingAllow: false,
      country: "",
      phone: "",
      email: "",
      petDetail: {
        typeId: null,
        sex: "",
        neutered: null,
        nickname: "",
        initialWeight: null,
        targetWeight: null,
        birth: "",
        adoptionDate: "",
        registrationNumber: null,
      },
    },

    // cym main
    urineTestResult: {
      cym702: [],
      blood: [],
      glucose: [],
      protein: [],
      ph: [],
      ketone: [],
    },
    cymRecentData: [],
    totalScore: 0,
    chartData: [],
    targetWater: 0,
    weight: { value: 0, createdAt: "0000.00.00" },
    careData: {
      weight: 0,
      water: "",
      pee: 0,
    },
    activeDropletIdx: 0,
    mainchartIdx: null,
    showCompleteModal: null, // 기본 값: Null, animation 실행 할 때 true, false 값 변경, home.vue에서 setup 함수를 통해 null로 초기화 (검사 모달 띄우지 않았을 때)
    showExamLoading: false,
    showExamErrorModal: false,
    username: "",
    userImg: "",
    phone: "",
    selectUser: 0,
    subUserId: 0,
    subUser: "",
    subUserGender: "",
    subUserBirth: "",
    subUserType: "",
    noSubjects: false,
    isBoat: true,

    // history data
    historyPage: 1,
    historyTotalPage: 5,
    historyLimit: 5,
    tooltipIndex: null,

    // urine test items history
    cymHistory: {
      cymAvg: "",
      cymScore: "",
      cymHistory: [],
      blood: [],
      glucose: [],
      protein: [],
      ph: [],
      ketone: [],
    },

    careHistory: {
      weightData: [],
      waterData: [],
      peeData: [],
      careDetailData: [],
    },

    openGuide: false,
    cardTitle: "",
    showCymscore: false,
    noDataLockModalState: false,
    userImage: null,
    alreadySigned: false,
    isSuccess: false,
    isEnableTouch: false,
    month: 0,
    openKetoneEditModal: false,

    // solution
    showSelectModal: false,
    selectedFood: {},
    selectedFoodId: 0,
    clickTitle: false,
    showSelectBtn: false,
    selectionBtn: false,
    showRecordSelectModal: false,
    recordSelectType: "",
    recordSelectBtn: false,
    filterBtn: false,
    bookmarklist: [],
    mealplan: {
      breakfastlist: [],
      lunchlist: [],
      dinnerlist: [],
    },
    FoodAmountPercentage: [],
  },
  mutations,
  actions,
  getters: {
    loggedIn: (state) => state.isLogin,
    accessToken: (state) => state.isLogin,
    activeDropletIdx: (state) => state.activeDropletIdx,
    showCompleteModal: (state) => state.showCompleteModal,
    blood: (state) => state.urineTestResult.blood,
    glucose: (state) => state.urineTestResult.glucose,
    protein: (state) => state.urineTestResult.protein,
    ph: (state) => state.urineTestResult.ph,
    ketone: (state) => state.urineTestResult.ketone,
    cymScore: (state) => state.cymHistory.cymScore,
    cymAvg: (state) => state.cymHistory.cymAvg,
    tooltipIdx: (state) => state.tooltipIndex,
  },
});
