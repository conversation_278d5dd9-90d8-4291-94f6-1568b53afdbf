<template>
  <div>
    <background>
      <HeaderNav
        page="wait"
        @goToCamera="goToCamera"
        @updateStopTimer="handleUpdateStopTimer"
        :startHeaderCamera="startHeaderCamera"
      />
      <!-- image section -->
      <div
        v-show="firstContents"
        :class="lang === 'ko' ? 'images-section' : 'images-section_en'"
      >
        <div class="image__wrapper">
          <!-- <img src="@/assets/images/guide_img/removing_pee.gif" alt="removing pee image" /> -->
          <transition name="fade">
            <div
              v-show="firstTextIndex >= 0"
              class="guide_text1"
              :class="lang === ko ? 'guide_ko' : 'guide_en'"
            >
              {{ $t("exam_guide_txt_one") }}
            </div>
          </transition>
          <transition name="fade">
            <div
              v-show="firstTextIndex >= 1"
              class="guide_bold_text"
              :class="lang === ko ? 'guide_ko' : 'guide_en'"
            >
              {{ $t("exam_guide_txt_two") }}
            </div>
          </transition>
          <transition name="fade">
            <div
              v-show="firstTextIndex >= 2"
              class="guide_text3"
              :class="lang === ko ? 'guide_ko' : 'guide_en'"
            >
              {{ $t("exam_guide_txt_three") }}
            </div>
          </transition>
        </div>
      </div>
      <div v-show="timeToContents" class="video-section">
        <div class="image__wrapper">
          <!-- <img :src="waitImg" alt="removing pee image" /> -->
          <img :src="guideTutorial" width="100%" />
        </div>
        <!-- <div class="image-txt__wrapper">
          <div>
            <transition name="slide-fade" mode="out-in">
              <h3 class="note_text" v-html="waitText"></h3>
            </transition>
          </div>
        </div> -->
      </div>

      <div class="exam-wait-content-section">
        <div class="timer__wrapper">
          <circular-count-down-timer
            class="timer"
            :initial-value="initSecond"
            :stroke-width="5"
            :seconds-stroke-color="'#41D8E6'"
            :underneath-stroke-color="'#F8F8F8'"
            :seconds-fill-color="'transparent'"
            :size="240"
            :padding="4"
            :second-label="''"
            :show-second="true"
            :show-minute="false"
            :show-hour="false"
            :show-negatives="true"
            :notify-every="'second'"
            :paused="stopTimer"
            @finish="startCamera"
            @update="checkSecond"
          ></circular-count-down-timer>
        </div>
      </div>

      <div class="wait-btn__wrapper">
        <div
          class="wait-btn"
          :class="lang === 'ko' ? 'guide_ko' : 'guide_en'"
          @click="skipNext"
        >
          <div>{{ $t("exam_notice_btn") }}</div>
        </div>
      </div>
    </background>
  </div>
</template>

<script>
import HeaderNav from "@/components/Exam/HeaderNav.vue";
import { getRefreshToken, setExp } from "@/api/auth/tokenModules";

export default {
  components: {
    HeaderNav,
  },
  data() {
    return {
      initSecond: 60,
      waitText: "",
      waitImg: "",
      firstContents: true,
      timeToContents: false,
      isPlay: true,
      lang: this.$i18n.locale.includes("ko") ? "ko" : "en",
      startHeaderCamera: false,
      waitTextArr: [
        this.$i18n.t("exam_guide_txt_one"),
        this.$i18n.t("exam_guide_txt_two"),
        this.$i18n.t("exam_guide_txt_three"),
      ],
      // waitContentsArr: [
      //   {
      //     img: require("@/assets/images/guide_img/transform_camera.gif"),
      //     text: this.$i18n.t("exam_notice_txt_one"),
      //   },
      //   {
      //     img: require("@/assets/images/guide_img/removing_pee.gif"),
      //     text: this.$i18n.t("exam_notice_txt_two"),
      //   },
      //   {
      //     img: require("@/assets/images/guide_img/mounting_boat.gif"),
      //     text: this.$i18n.t("exam_notice_txt_three"),
      //   },
      //   {
      //     img: require("@/assets/images/guide_img/cleaning_lens.gif"),
      //     text: this.$i18n.t("exam_notice_txt_four"),
      //   },
      //   {
      //     img: require("@/assets/images/guide_img/takingPicture_inGuideLine.gif"),
      //     text: this.$i18n.t("exam_notice_txt_five"),
      //   },
      //   {
      //     img: require("@/assets/images/guide_img/no_dim.gif"),
      //     text: this.$i18n.t("exam_notice_txt_six"),
      //   },
      // ],
      GuideTutorial: "",
      textIndex: 0,
      firstTextIndex: 0,
      stopTimer: false,
      cameraAction: true,
    };
  },
  mounted() {
    // this.waitText = this.waitTextArr[this.firstTextIndex];
    this.guideTutorial = require(`@/assets/images/guide_img/guide_tutorial_${this.lang}.gif`);

    const interval = setInterval(() => {
      this.firstTextIndex++;
      // console.log(this.firstTextIndex);
      if (this.firstTextIndex === 5) {
        clearInterval(interval);
      }
    }, 1000);
  },

  methods: {
    goBackHome() {
      this.$router.push({ path: "/home" });
    },
    checkSecond(status) {
      this.nowSecond = status.value;
      if (status.value === 0) {
        this.stopTimer = true;
        this.isPlay = false;
      }
      if (status.value === 54) {
        this.timeToContents = true;
        this.firstContents = false;
        this.waitText = this.waitContentsArr[this.textIndex].text;
        this.waitImg = this.waitContentsArr[this.textIndex].img;
        const interval = setInterval(() => {
          if (this.isPlay) {
            this.textIndex++;
            if (this.textIndex === 6) {
              // this.textIndex = 0;
              clearInterval(interval);
            }
            this.waitText = this.waitContentsArr[this.textIndex].text;
            this.waitImg = this.waitContentsArr[this.textIndex].img;
          } else {
            clearInterval(interval);
          }
        }, 9000);
      }
    },
    skipNext() {
      // console.log("do something");
    },
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId =
        subjects !== null
          ? subjects[selectedId].id
          : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    async startCamera() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      const subjectId = this.getSubjectId();
      const token = await getRefreshToken();
      localStorage.auth = token;
      this.$store.commit("SET_TOKEN", token);
      setExp(token);

      const message = {
        accessToken: token,
        subjectId: subjectId,
        description: this.$i18n.t("analysis_description"),
        buttonText: this.$i18n.t("direct_shot"),
        previewDescription: this.$i18n.t("shot_confirm_description"),
        previewOk: this.$i18n.t("yes"),
        previewCancel: this.$i18n.t("no"),
      };
      Webview.requestCamera(message);
      this.startHeaderCamera = true;
      this.stopTimer = true;
    },
    goToCamera() {
      // this.stopTimer = true;
      this.initSecond = 60;
      this.cameraAction = false;
    },
  },
};
</script>

<style scoped>
.images-section {
  height: 410px;
  min-height: 410px;
  padding-top: 110px;
}

.images-section_en {
  height: 410px;
  min-height: 410px;
  padding-top: 110px;
}

.image__wrapper {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
}

.image__wrapper > img {
  width: 100%;
}

.guide_text1 {
  color: #000000;
  font-size: 32px;
  font-weight: 500;
  letter-spacing: -0.03em;
  position: absolute;
  top: 175px;
  transition: opacity 0.5s linear;
}
.guide_bold_text {
  color: #41d8e6;
  font-size: 40px;
  font-weight: 700;
  letter-spacing: -0.03em;
  position: absolute;
  top: 220px;
  transition: opacity 0.5s linear;
}
.guide_text3 {
  color: #000000;
  font-size: 32px;
  font-weight: 500;
  letter-spacing: -0.03em;
  position: absolute;
  top: 275px;
  transition: opacity 0.5s linear;
}

.image-txt__wrapper {
  width: 100%;
  height: 80px;
  position: absolute;
  bottom: -60px;
  background-color: #c9f4f8;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 30px;
}

.note_text {
  color: #000000;
  text-align: center;
  font-weight: 500;
  font-size: 20px;
  line-height: 20px;
}

.guide1-content-section .guide1-content-title__wrapper {
  width: 100%;
  text-align: left;
  font-style: normal;
  font-weight: 600;
  font-size: 22px;
  line-height: 27px;
}

.exam-wait-content-section {
  width: 100%;
  height: 400px;
}

.timer__wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

::v-deep #container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.wait-btn__wrapper {
  width: 100%;
  position: absolute;
  bottom: 50px;
  left: 0;
  padding: 0 30px;
}

.exam-next-btn {
  width: 100%;
  height: 50px;
  color: #fff;
  background: #41d8e6;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  font-weight: 700;
  font-size: 20px;
}

.timer {
  padding: 0;
}

::v-deep #container .item > div {
  font-style: normal;
  font-weight: normal;
  font-size: 60px !important;
  line-height: 200px !important;
}

.timer-guide {
  text-align: center;
}
.timer-guide-text {
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.03em;
  color: #000000;
}

.wait-btn {
  font-size: 20px;
  line-height: 29px;
  letter-spacing: -0.03em;
  width: 100%;
  height: 50px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #41d8e6;
  color: #c9f4f8;
}

.guide_ko {
  font-family: Noto Sans KR !important;
  font-weight: 700;
}

.guide_en {
  font-family: GilroyBold !important;
}

.fade-enter {
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.8s ease-out;
}

.fade-leave-to {
  opacity: 0;
}

.video-section {
  height: 410px;
  min-height: 410px;
  padding-top: 110px;
}

.guide_ko {
  font-family: Noto Sans KR !important;
  font-weight: 500;
}

.guide_en {
  font-family: GilroyBold !important;
}
</style>
