<template>
  <div>
    <Loading v-if="loading" />
    <background v-if="loaded">
      <!-- TODO: 🔖 header component -->
      <div>
        <Header />
        <div class="history-header">
          <div class="history-header__wrapper">
            <div :class="isIos ? 'history-header_nav' : 'history-header_nav-android'">
              <router-link to="/home"><v-icon>$back_btn_bold</v-icon></router-link>
            </div>
            <div class="history-header_title">
              <PetLogo style="width: 50px" />
              History
            </div>
          </div>
        </div>
        <div class="tab-container">
          <v-tabs
            v-model="tabName"
            color="#C9F4F8"
            class="history-tabs"
            center-active
            grow
            @change="changeTabs"
            mobile-breakpoint="xs"
            slider-color="#41D8E6"
            slider-size="10"
            height="40px"
          >
            <v-tab
              class="mx-0"
              :class="
                tabName === 'tab-cym702' ? (isKo ? 'v-tab-active-ko' : 'v-tab-active-en') : ''
              "
              href="#tab-cym702"
              >{{ $t("tab_cym") }}</v-tab
            >

            <v-tab
              class="mx-0"
              :class="tabName === 'tab-blood' ? (isKo ? 'v-tab-active-ko' : 'v-tab-active-en') : ''"
              href="#tab-blood"
              >{{ $t("tab_blood") }}</v-tab
            >

            <v-tab
              id="ph-text"
              class="mx-0"
              :class="tabName === 'tab-ph' ? (isKo ? 'v-tab-active-ko' : 'v-tab-active-en') : ''"
              href="#tab-ph"
              >{{ $t("tab_ph") }}</v-tab
            >

            <v-tab
              class="mx-0"
              :class="
                tabName === 'tab-protein' ? (isKo ? 'v-tab-active-ko' : 'v-tab-active-en') : ''
              "
              href="#tab-protein"
              >{{ $t("tab_protein") }}</v-tab
            >

            <v-tab
              class="mx-0"
              :class="
                tabName === 'tab-glucose' ? (isKo ? 'v-tab-active-ko' : 'v-tab-active-en') : ''
              "
              href="#tab-glucose"
              >{{ $t("tab_glucose") }}</v-tab
            >

            <v-tab
              class="mx-0"
              :class="
                tabName === 'tab-ketone' ? (isKo ? 'v-tab-active-ko' : 'v-tab-active-en') : ''
              "
              href="#tab-ketone"
              >{{ $t("tab_ketone") }}</v-tab
            >

            <v-tab
              class="mx-0"
              :class="
                tabName === 'tab-leukocytes' ? (isKo ? 'v-tab-active-ko' : 'v-tab-active-en') : ''
              "
              href="#tab-leukocytes"
              >{{ $t("tab_leukocytes") }}</v-tab
            >

            <v-tab
              class="mx-0"
              :class="
                tabName === 'tab-bilirubin' ? (isKo ? 'v-tab-active-ko' : 'v-tab-active-en') : ''
              "
              href="#tab-bilirubin"
              >{{ $t("tab_bilirubin") }}</v-tab
            >

            <v-tab
              class="mx-0"
              :class="
                tabName === 'tab-urobilinogen' ? (isKo ? 'v-tab-active-ko' : 'v-tab-active-en') : ''
              "
              href="#tab-urobilinogen"
              >{{ $t("tab_urobilinogen") }}</v-tab
            >

            <v-tab
              class="mx-0"
              :class="
                tabName === 'tab-nitrite' ? (isKo ? 'v-tab-active-ko' : 'v-tab-active-en') : ''
              "
              href="#tab-nitrite"
              >{{ $t("tab_nitrite") }}</v-tab
            >

            <v-tab
              class="mx-0"
              :class="tabName === 'tab-sg' ? (isKo ? 'v-tab-active-ko' : 'v-tab-active-en') : ''"
              href="#tab-sg"
              >{{ $t("tab_sg") }}</v-tab
            >
          </v-tabs>
        </div>
      </div>

      <v-tabs-items v-model="tabName" color="transparent" :touchless="true">
        <v-tab-item value="tab-cym702" class="tab-item">
          <Cym702
            :historyData="cym"
            :totalCount="totalCount"
            :avgScore="avgScore"
            :startDate="startDate"
            :endDate="endDate"
            @pageHandler="pageHandler"
            @limitHandler="limitHandler"
            :loading="loading"
            v-if="loaded"
          />
        </v-tab-item>
        <v-tab-item value="tab-blood" class="tab-item">
          <UrineTestItemView type="blood" :historyData="blood" :loading="loading" v-if="loaded" />
        </v-tab-item>
        <v-tab-item value="tab-ph" class="tab-item">
          <UrineTestItemView type="ph" :historyData="ph" :loading="loading" v-if="loaded" />
        </v-tab-item>
        <v-tab-item value="tab-protein" class="tab-item">
          <UrineTestItemView
            type="protein"
            :historyData="protein"
            :loading="loading"
            v-if="loaded"
          />
        </v-tab-item>
        <v-tab-item value="tab-glucose" class="tab-item">
          <UrineTestItemView
            type="glucose"
            :historyData="glucose"
            :loading="loading"
            v-if="loaded"
          />
        </v-tab-item>
        <v-tab-item value="tab-ketone" class="tab-item">
          <UrineTestItemView type="ketone" :historyData="ketone" :loading="loading" v-if="loaded" />
        </v-tab-item>
        <v-tab-item value="tab-leukocytes" class="tab-item">
          <UrineTestItemView
            type="leukocytes"
            :historyData="leukocytes"
            :loading="loading"
            v-if="loaded"
          />
        </v-tab-item>
        <v-tab-item value="tab-bilirubin" class="tab-item">
          <UrineTestItemView
            type="bilirubin"
            :historyData="bilirubin"
            :loading="loading"
            v-if="loaded"
          />
        </v-tab-item>
        <v-tab-item value="tab-urobilinogen" class="tab-item">
          <UrineTestItemView
            type="urobilinogen"
            :historyData="urobilinogen"
            :loading="loading"
            v-if="loaded"
          />
        </v-tab-item>
        <v-tab-item value="tab-nitrite" class="tab-item">
          <UrineTestItemView
            type="nitrite"
            :historyData="nitrite"
            :loading="loading"
            v-if="loaded"
          />
        </v-tab-item>
        <v-tab-item value="tab-sg" class="tab-item">
          <UrineTestItemView type="sg" :historyData="sg" :loading="loading" v-if="loaded" />
        </v-tab-item>
      </v-tabs-items>
    </background>
    <NoneTestModal v-if="showNoDataModal" :isTenAnalysisOptions="isTenAnalysisOptions" />
    <Navigation :path="path" />
    <CymScore v-if="showGuide" />
    <div class="snackbar">
      <v-snackbar v-model="saveSuccess" timeout="2000">{{ successContent }}</v-snackbar>
      <v-snackbar v-model="saveFail" timeout="2000" color="#EE0000">{{ failContent }}</v-snackbar>
    </div>
  </div>
</template>

<script>
// history components
import Header from "@/components/History/Header.vue";
import Cym702 from "@/components/History/Cym702.vue";
import UrineTestItemView from "@/components/History/UrineTestItemView.vue";
import CymScore from "@/components/History/CymScore.vue";

import Loading from "@/components/Common/Loading.vue";

import PetLogo from "@/assets/images/pet/pet.svg";

import NoneTestModal from "@/components/History/NoneTestModal.vue";

// common components
import Background from "@/components/Common/Background.vue";
import Navigation from "@/components/Common/Navigation.vue";

// import EditValueModal from "@/components/Care/EditWaterValueModal.vue";

// API
import API from "@/api/cym702/index.js";
import { updateSubjectInfo } from "@/api/user/index.js";
import dataProcessing from "@/assets/data/manufacturing/cym.js";

export default {
  components: {
    Header,
    Background,
    Cym702,
    UrineTestItemView,
    Navigation,
    CymScore,

    PetLogo,
    NoneTestModal,
    // EditValueModal,
    Loading,
  },
  data() {
    return {
      currentScrollValue: 0,
      tabName: "tab-cym702",
      path: "/home",
      loading: false,
      loaded: false,
      currentTabIndex: "",
      count: "recent",
      totalCount: 0,
      historyData: [],
      cymData: [],
      avgData: [],
      cym: [],
      blood: [],
      glucose: [],
      protein: [],
      ph: [],
      ketone: [],
      leukocytes: [],
      bilirubin: [],
      urobilinogen: [],
      nitrite: [],
      sg: [],
      totalDataLen: 0,
      recentCount: 0,
      yearCount: 0,
      avgScore: 0,
      startDate: "",
      endDate: "",
      page: 1,
      limit: 5,
      showKetoneGuide: true,
      showEditModal: false,
      saveSuccess: false,
      successContent: this.$i18n.t("save_success"),
      saveFail: false,
      failContent: this.$i18n.t("save_fail"),
      isKo: this.$i18n.locale.includes("ko") || this.$i18n.locale.includes("cn"),
      isIos:
        navigator.userAgent.toLowerCase().indexOf("iphone") > -1 ||
        navigator.userAgent.toLowerCase().indexOf("ipad") > -1,
    };
  },

  computed: {
    isTenAnalysisOptions() {
      const tenAnalysisTabs = [
        "tab-leukocytes",
        "tab-bilirubin",
        "tab-urobilinogen",
        "tab-nitrite",
        "tab-sg",
      ];
      return tenAnalysisTabs.includes(this.tabName);
    },
    showGuide() {
      return this.$store.state.showCymscore;
    },
    showNoDataModal() {
      return this.$store.state.noDataLockModalState;
    },
    enableTouchless() {
      if (this.$store.state.isEnableTouch) {
        return true;
      } else {
        return false;
      }
    },
    historyPage() {
      return this.$store.state.historyPage;
    },
    historyTotalPage: {
      get() {
        return this.$store.state.historyTotalPage;
      },
      set(value) {
        this.$store.commit("SET_HISTORY_TOTAL_PAGE", value);
      },
    },
    historyLimit() {
      return this.$store.state.historyLimit;
    },
  },

  watch: {
    avgScore(newVal) {
      console.log(newVal);
    },
    historyPage(newVal) {
      console.log("history page", newVal);
      console.log("241, this.tabName", this.tabName);
      this.tabName.includes("cym702")
        ? this.fetchCymScoreData(newVal, this.historyLimit)
        : this.reFetchUrinalysisData(this.tabName, newVal, this.historyLimit);
      this.$store.commit("SET_TAB_STATE", true);
    },
    historyLimit(newVal) {
      console.log("history limit", newVal);
      this.tabName.includes("cym702")
        ? this.fetchCymScoreData(this.historyPage, newVal)
        : this.reFetchUrinalysisData(this.tabName, this.historyPage, newVal);
      this.$store.commit("SET_TAB_STATE", true);
    },
    totalPage() {},
  },

  methods: {
    getSelectNum(selectNum) {
      return selectNum;
    },
    toggleScroll(state) {
      // state
      //   ? document.body.classList.add("no-scroll")
      //   : document.body.classList.remove("no-scroll");
    },
    cancelHandler() {
      this.toggleScroll(false);
      this.showKetoneGuide = false;
      localStorage.setItem("ketoneGuide", false);
    },

    changeTabs(tabIndex) {
      this.$store.commit("SET_TAB_STATE", true);
      this.$store.commit("setHistoryTabChanged", tabIndex);
      this.currentTabIndex = tabIndex;
      tabIndex.includes("cym702")
        ? this.fetchCymScoreData(this.historyPage, this.historyLimit)
        : this.reFetchUrinalysisData(tabIndex, this.historyPage, this.historyLimit);
    },
    pageHandler(fromChild) {
      console.log("cym page", fromChild);
    },
    limitHandler(fromChild) {
      console.log("cym limit", fromChild);
    },
    lockHandler() {
      // open dim
      console.log("hello");
    },

    getAvgScore(score) {
      // console.log("avg score func", score);
      let avg = score.reduce((acc, cur) => acc + Number(cur.value), 0);
      avg = (avg / score.length).toFixed();
      console.log("avg");
      console.log(avg);
      this.avgScore = Number(avg);
    },

    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId =
        subjects !== null ? subjects[selectedId].id : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },

    async fetchCymScoreData(page, limit) {
      // this.loaded = false;
      this.loading = true;
      const subjectId = this.getSubjectId();
      try {
        const { data, status } = await API.GetCymScoreData(subjectId, page, limit);
        if (status === 200) {
          this.cym = dataProcessing.MAKE_CYMSCORE_DATA(data.result);
          this.totalCount = data.result.length;
          this.avgScore = data.avgCymScore ?? 0;
          const totalPage = Math.ceil(data.count / limit);
          this.$store.commit("SET_HISTORY_TOTAL_PAGE", totalPage);
          console.log(this.cym);
          this.changeData();
        }
        this.loaded = true;
        this.loading = false;
      } catch (error) {
        console.error(error);
        this.loaded = true;
        this.loading = false;
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    async fetchUrinalysisData(tabIdx) {
      this.loading = true;
      const subjectId = this.getSubjectId();
      const tab = Number(tabIdx);
      const tabArr = [
        "cym",
        "blood",
        "ph",
        "protein",
        "glucose",
        "ketone",
        "leukocytes",
        "bilirubin",
        "urobilinogen",
        "nitrite",
        "sg",
      ];
      const cymType = tabArr[tab];
      console.log(cymType);
      try {
        const { data, status, config } = await API.GetUrinalysisData(
          subjectId,
          cymType,
          this.page,
          this.$store.state.historyLimit || this.limit,
        );
        console.log(data, status, config.url);
        if (data) {
          this[cymType] = dataProcessing.MAKE_HISTORY_DATA(data.result, cymType);
          const totalPage = Math.ceil(data.count / this.limit);
          this.$store.commit("SET_HISTORY_TOTAL_PAGE", totalPage);
        }
        this.loaded = true;
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loaded = true;
        this.loading = false;
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    async reFetchUrinalysisData(tabName, page, limit) {
      console.log("tabname here?", tabName);
      const cymType = tabName.split("-")[1];
      console.log(cymType);
      // this.loaded = false;
      this.loading = true;
      const subjectId = this.getSubjectId();
      try {
        const { data, status, config } = await API.GetUrinalysisData(
          subjectId,
          cymType,
          page,
          limit,
        );
        console.log(data, status, config.url);
        if (data) {
          this[cymType] = dataProcessing.MAKE_HISTORY_DATA(data.result, cymType);
          const totalPage = Math.ceil(data.count / limit);
          this.$store.commit("SET_HISTORY_TOTAL_PAGE", totalPage);
        }
        this.loaded = true;
        this.loading = false;
      } catch (error) {
        console.error(error);
        this.loaded = true;
        this.loading = false;
        if (error.response && error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },

    initialFetch() {
      this.loading = true;
      const tab = this.$route.query.tab;
      console.log("tab idx", tab);
      tab === undefined
        ? this.fetchCymScoreData(this.historyPage, this.historyLimit)
        : this.fetchUrinalysisData(tab);
    },

    changeData() {
      const len = this.cym.length;

      if (len === 0) {
        return;
      }

      console.log(this.cym);
      let [y, m, d] = this.cym[len - 1].createdAt.slice(0, 10).split("-");
      console.log(typeof y);
      y = String(y).slice(2);
      this.endDate = `${y}.${m}.${d}`;
      let [startYear, startMonth, startDay] = this.cym[0].createdAt.slice(0, 10).split("-");
      startYear = String(startYear).slice(2);
      this.startDate = `${startYear}.${startMonth}.${startDay}`;
    },
  },

  created() {
    window.scrollTo(0, 0);
    const tabNames = [
      "cym702",
      "blood",
      "ph",
      "protein",
      "glucose",
      "ketone",
      "leukocytes",
      "bilirubin",
      "urobilinogen",
      "nitrite",
      "sg",
    ];
    const tab = this.$route.query.tab;

    if (tab === undefined) {
      this.tabName = "tab-cym702";
      return;
    }

    this.tabName = `tab-${tabNames[Number(tab)]}`;
  },
  beforeMount() {
    this.loading = true;
  },
  mounted() {
    const limit = this.limit;

    this.initialFetch();
  },

  beforeDestroy() {
    this.$store.commit("closeGuideModal");
  },
};
</script>

<style lang="scss" scoped>
// .overlay {
//   position: fixed;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   background-color: rgba(0, 0, 0, 0.8);
//   z-index: 99999;
// }

.overlay {
  position: absolute;
  width: calc(100vw - 40px);
  padding: 0 30px;
  height: 98px;
  // top: 50px;
  left: 20px;
  border-radius: 10px;
  box-shadow: 0 0 0 100vmax rgba(0, 0, 0, 0.8);
  // bottom: 150px;
  top: 68vh;
  z-index: 9999998;
}

.bg-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  opacity: 1;
  z-index: 9999998;
  // overflow-y: auto;
  // -webkit-overflow-scrolling: touch;
  // padding: 15px 15px;
  // background: rgba(0, 0, 0, 0.8);
  // transition: opacity 0.15s linear;
  // display: flex;
  // align-items: center;
  // justify-content: center;
  // padding: 30px;
}

.close-icon__wrapper {
  z-index: 9999999;
  text-align: left;
  position: absolute;
  top: 65px;
  left: 30px;
  img {
    width: 26px;
  }
}

.arrow-icon__wrapper {
  z-index: 9999999;
  position: absolute;
  // bottom: 270px;
  top: 62vh;
  right: 30px;
  img {
    width: 18px;
    transform: rotate(180deg);
  }
}

.guide-contents {
  position: absolute;
  text-align: right;
  // top: 0;
  right: 30px;
  top: 52vh;
  z-index: 9999999;
  color: #ffffff;
  font-size: 18px;
  margin: 20px 0 30px;
}

.bold {
  font-weight: 700;
}

.guide-desc__wrapper {
  /* width: 100%; */
  padding: 0 20px;
  img {
    width: 100%;
  }
}

::v-deep .v-tabs-slider-wrapper {
  color: #41d8e6;
  height: 3px !important;
}

::v-deep .v-tabs-slider {
  overflow-x: visible !important;
  border-radius: 3px 3px 0px 0px !important;
}

::v-deep .v-tabs-bar {
  overflow-x: auto !important;
  white-space: nowrap;
}

::v-deep .v-tab {
  color: rgba(0, 0, 0, 0.32) !important;
  font-weight: 500 !important;
  line-height: 40px;
  letter-spacing: -0.05em !important;
  text-transform: none !important;
  font-size: 18px !important;
}
::v-deep .v-tab--active {
  font-size: 18px !important;
  font-weight: bold !important;
  color: #000000 !important;
}

::v-deep .v-tab-active-ko {
  font-family: Noto Sans KR !important;
}

::v-deep .v-tab-active-en {
  font-family: GilroyBold !important;
}

@media screen and (max-width: 375px) {
  ::v-deep .v-tab {
    font-size: 16px !important;
  }
  ::v-deep .v-tab--active {
    font-size: 16px !important;
  }
}
// ::v-deep .v-tabs {
//   margin-top: 10px;
// }

::v-deep .v-tabs-bar__content {
  background: transparent !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}

::v-deep .theme--light.v-tabs > .v-tabs-bar {
  background-color: transparent !important;
}

::v-deep .v-toolbar__content {
  height: auto !important;
}

::v-deep .v-tab {
  min-width: 40px !important;
  max-width: 16.5% !important;
  background-color: transparent !important;
  /* padding: 0 1%; */
}

// tab ripple 제거
::v-deep .v-ripple__container {
  display: none !important;
}

::v-deep .v-input--selection-controls__ripple {
  display: none !important;
}

::v-deep .v-tab:before {
  display: none !important;
}

::v-deep .theme--light.v-tabs-items {
  background-color: transparent !important;
}

.history-header {
  padding: 0px 30px;
}

.history-header_title {
  font-family: GilroyBold;
  font-size: 36px;
  text-align: left;
  // font-weight: bold;
  color: #000000;

  img {
    width: 50px;
  }
}

.tab-container {
  min-width: 100%;
  padding: 10px 3px 0 3px;
}

.history-header_nav {
  display: flex;
  justify-content: flex-start;
  padding-top: 65px;
}
.history-header_nav-android {
  display: flex;
  justify-content: flex-start;
  padding-top: 50px;
}

::v-deep
  .v-tabs:not(.v-tabs--vertical):not(.v-tabs--right)
  > .v-slide-group--is-overflowing.v-tabs-bar--is-mobile:not(.v-slide-group--has-affixes)
  .v-slide-group__prev {
  display: none;
}

// snackbar custom
.snackbar {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 10px);
  z-index: 10000;
}

::v-deep .v-snack__wrapper {
  min-width: 220px;
  width: 220px !important;
  height: 55px;
  max-width: 220px;
}
::v-deep .v-sheet.v-snack__wrapper:not(.v-sheet--outlined) {
  box-shadow: none;
}

::v-deep .v-snack__wrapper.theme--dark {
  background-color: #000000;
  opacity: 0.8 !important;
  margin: 0;
}

::v-deep .v-sheet.v-snack__wrapper {
  border-radius: 5px;
}

::v-deep .v-snack__content {
  padding: 0 0 3px 10px !important;
  // width: 210px !important;
  line-height: 29px;
  display: flex;
  justify-content: center;
  text-align: center;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: -0.03em;
}
</style>
