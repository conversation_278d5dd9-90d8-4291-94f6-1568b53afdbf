<template>
  <Background>
    <AlertModal
      v-if="showAlertModal"
      @closeBtnHandler="closeBtnHandler"
      @nextHandler="surveyHandler"
      :btnText="goToSurvey"
      :error="error"
    />
    <!-- header section -->
    <Header />
    <!-- chart and total score view -->
    <TotalAndChartView
      v-if="loaded"
      :graphData="graphData"
      :username="username"
      :avgCymScore="avgCymScore"
    />

    <!-- 5종 검사결과 -->
    <!-- <white-card>
      <UrineTestResultView
        @pageHandler="pageHandler"
        v-if="loaded"
        :loaded="loaded"
        :historyData="historyData"
        :totalCount="totalCount"
      />
    </white-card> -->

    <!-- 10종 검사결과 -->
    <white-card>
      <TenUrineTestResultView
        v-if="loaded"
        :loaded="loaded"
        :historyData="historyData"
        :count="totalCount"
        @pageHandler="pageHandler"
      />
    </white-card>
    <div class="pt-40"></div>

    <!-- 체중, 수분, 배뇨케어 컨트롤 버튼 -->
    <white-card v-show="showCare">
      <WeightCareView :weight="weight" v-if="loaded" @reloadHome="reloadHome" />
      <!-- <WaterCareView :water="water" v-if="loaded" @reloadHome="reloadHome" /> -->
      <PeeCareView :pee="urine" v-if="loaded" @reloadHome="reloadHome" />
    </white-card>

    <!-- set functional components -->
    <Navigation :path="path" />
    <CompleteAlert
      v-if="showCompleteModal"
      :totalScore="totalScore"
      :username="username"
      @closeOverlayHandler="closeOverlayHandler"
    />
    <!-- <ScrollDownBtn
      v-if="showScrollDownSign && !showCompleteModal && showCare && loaded"
      :showCompleteModal="showCompleteModal"
    /> -->
    <NoticeModal
      :content="content"
      v-if="showNoticeModal"
      :btnText="btnText"
      @isConfirmed="isConfirmed"
    />
    <Loading v-if="loading" />
  </Background>
</template>

<script>
import AlertModal from "@/components/Common/AlertModal.vue";
import NoticeModal from "@/components/Common/ConfirmModal.vue";
// page section components
import TotalAndChartView from "./sections/TotalAndChart.view.vue";
import UrineTestResultView from "./sections/UrineTestResult.view.vue";
import WeightCareView from "./sections/CareWeightControls.view.vue";
import TenUrineTestResultView from "./sections/TenUrineTestResult.view.vue";
// import WaterCareView from "./sections/CareWaterControls.view.vue";
import PeeCareView from "./sections/CarePeeControls.view.vue";

// functional components
import Header from "@/components/Home/Header.vue";
import Navigation from "@/components/Common/Navigation.vue";
import WhiteCard from "@/components/Common/WhiteCard.vue";
import Background from "@/components/Common/Background.vue";
import CompleteAlert from "@/components/Home/CompleteAlert.vue";
import ScrollDownBtn from "./sections/ScrollDownBtn.vue";
import Loading from "@/components/Common/Loading.vue";

import API from "@/api/cym702/index";
// import CAREAPI from "@/api/care/index";
import dataProcessing from "@/assets/data/manufacturing/cym.js";

export default {
  components: {
    AlertModal,
    NoticeModal,

    TotalAndChartView,
    // UrineTestResultView,
    TenUrineTestResultView,
    WeightCareView,
    // WaterCareView,
    PeeCareView,

    Header,
    Navigation,
    WhiteCard,
    Background,
    CompleteAlert,
    // ScrollDownBtn,
    Loading,
  },

  data() {
    return {
      loaded: false,
      loading: false,
      path: "/home",
      showScrollDownSign: true,
      showCompleteAlert: false,
      totalScore: 0,
      graphData: [],
      historyData: [],
      username: "",
      weight: 0,
      water: 0,
      urine: 0,
      selectUser: 0,
      avgCymScore: 0,
      totalCount: 0,
      showCare: true,
      showAlertModal: false,
      error:
        "소변검사와 맞춤형 솔루션을<br/>위해 설문 작성이 필요합니다.<br/>잠시만 시간을 내주세요!😊",
      goToSurvey: "설문 작성하러 가기",
      content: this.$i18n.t("update_description"),
      btnText: this.$i18n.t("confirm_btn"),
      showNoticeModal: false,
      showAnimation: false,
    };
  },
  computed: {
    showCompleteModal() {
      return this.$store.state.showCompleteModal;
    },
  },
  watch: {
    showScrollDownSign() {
      document.addEventListener("scroll", () => {
        const currentScrollValue = document.documentElement.scrollTop;
        if (currentScrollValue > 0) {
          this.showScrollDownSign = false;
        }
      });
    },
  },
  methods: {
    getActiveIndex() {
      return this.$store.state.activeDropletIdx;
    },
    isConfirmed() {
      localStorage.isCheck = "checked";
      this.showNoticeModal = false;
    },
    closeBtnHandler(fromChild) {
      // console.log(fromChild);
      this.showAlertModal = false;
    },
    closeOverlayHandler() {},
    surveyHandler(fromChild) {
      // console.log(fromChild);
      this.$router.push("/survey");
    },
    pageHandler(page) {
      console.log(page);

      this.reloadCymData(page);
    },
    async reloadCymData(page) {
      const subjectId = this.getSubjectId();
      try {
        const { data, status, config } = await API.getAnalysisScoreData(subjectId, page, 5);
        // console.log(data, config.url);
        console.log(data);
        const cym = data.result;
        const graphData = dataProcessing.FETCH_CYM_DATA(cym);

        this.$store.commit("GET_RECENT_CYMDATA", data.result);

        // console.log("graphData get:", graphData);

        this.graphData = graphData.cym;
        this.historyData = data.result;
        // this.$store.commit("GET_ACTIVE_DROPLET_IDX", 4);
      } catch (error) {
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
        console.error(error);
      }
    },
    async getHistoryData(page) {
      const subjectId = this.getSubjectId();
      this.loaded = false;
      this.loading = true;

      try {
        if (subjectId !== null) {
          const { data, status, config } = await API.getAnalysisScoreData(subjectId, page, 5);
          // console.log(data, status, config.url);
          // store에 최근 5개 데이터 저장하고 history페이지에서 사용
          if (data) {
            // console.log("homeData get", data);
            // this.loading = false;
            localStorage.setItem("username", data.nickname);
            this.username = data.nickname;
            this.$store.commit("getUsername", data.nickname);
            this.$store.commit("GET_RECENT_CYMDATA", data.result);
            this.totalCount = data.count;
            if (data.result.length === 0) {
              // console.log("length 0");
              this.graphData = [];
              this.historyData = [];
            } else {
              const cym = data.result;
              const graphData = dataProcessing.FETCH_CYM_DATA(cym);

              if (page === 1) {
                this.$store.commit("setLastAnalysisTime", data.result[0].createdAt);
              }

              this.avgCymScore = data.avgCymScore;
              // console.log("graphData get:", graphData);
              this.graphData = graphData.cym;
              this.historyData = data.result;

              // tooltipIndex 설정 (response.result.length - 1)
              const tooltipIndex = data.result.length - 1;
              this.$store.commit("SET_TOOLTIP_INDEX", tooltipIndex);
            }
            this.$nextTick(() => {
              this.getCareValueData();
            });
          } else {
            this.graphData = [];
            this.historyData = [];
            this.$nextTick(() => {
              this.getCareValueData();
            });
          }
        } else {
          this.loading = false;
          this.loaded = true;
          this.graphData = [];
          this.historyData = [];
        }
      } catch (error) {
        console.error(error);
        this.loading = false;
        this.loaded = true;
        this.graphData = [];
        this.historyData = [];
        if (error.response.status === 429) {
          this.$store.commit("setoverReqModal", true);
        }
      }
    },
    formatDate() {
      // 호출하는 시점의 unix time 저장
      const unixTime = new Date().getTime();
      const curLocalTime = new Date(unixTime);
      const year = curLocalTime.getFullYear();
      const month = curLocalTime.getMonth();
      const day = curLocalTime.getDate();
      const hours = curLocalTime.getHours();
      const minutes = curLocalTime.getMinutes();
      const today = curLocalTime.toISOString();
      return [year, month, day, hours, minutes, today];
    },

    async getCareValueData() {
      // const data = this.formatDate();
      const subjectId = this.getSubjectId();
      // console.log(subjectId);
      let [y, m, d, hh, mm, today] = this.formatDate();
      const utcStart = new Date(y, m, d).toISOString();
      // console.log(utcStart);
      // console.log(y, m, d, hh, mm, today);
      try {
        const { data, config } = await API.GetHomeCareData(subjectId, utcStart);
        console.log(data);
        // console.log("care data:", data, config.url);
        if (data) {
          this.loading = false;
          this.loaded = true;

          // if (data.water.length === 0) {
          //   this.water = 0;
          // } else if (data.water.length === 1) {
          //   this.water = Number(data.water[0].value);
          // } else {
          //   const waterData = data.water.map((item) => item.value);
          //   this.water = waterData.reduce((a, b) => a + b);
          // }

          if (data.urine.length === 0) {
            this.urine = 0;
          } else {
            this.urine = data.urine.length;
          }

          if (data.weight === null) {
            this.weight = 0;
          } else {
            this.weight = Number(data.weight.value);
          }

          // this.weight = Number(data.weight.value);
          this.$store.commit("GET_CUR_WEIGHT", this.weight);
        } else {
          // this.water = 0;
          // this.urine = 0;
          this.weight = 0;
          this.loading = false;
          this.loaded = true;
        }
      } catch (error) {
        this.loading = false;
        this.loaded = true;
        console.log(error);
      }
    },

    scrollEvent(e) {
      if (window.scrollY > 0) {
        this.showScrollDownSign = false;
      }
    },
    reloadHome() {
      this.getSubjectId();
    },
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId =
        subjects !== null ? subjects[selectedId]?.id : Number(localStorage.getItem("subjectId"));
      return subjectId || null;
    },
  },
  created() {
    this.getHistoryData(1);
    this.$store.commit("GET_ACTIVE_DROPLET_IDX", 0);
  },
  beforeMount() {
    // this.loading = true;
  },
  mounted() {
    const check = localStorage.isCheck;

    this.showNoticeModal = check === undefined;
    this.$store.commit("setShowExamLoading", false);
    this.$store.commit("setShowExamErrorModal", false);
    window.scrollTo(0, 0);
    document.body.classList.remove("no-scroll");
    document.addEventListener("scroll", this.scrollEvent);

    if (
      this.$store.state.showCompleteModal !== null &&
      this.$store.state.showCompleteModal === false
    ) {
      this.$store.commit("SETUP_COMPLETE_MODAL");
    }
  },
};
</script>

<style scoped></style>
