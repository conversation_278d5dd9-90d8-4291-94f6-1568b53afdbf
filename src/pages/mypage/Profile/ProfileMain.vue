<template>
  <div>
    <Loading v-if="loading" />
    <HeaderNav class="empty-background" :pageName="pageName" />
    <!-- <CompleteAlert
      :content="content"
      :btnText="this.$i18n.t('confirm_btn')"
      v-if="showCompleteAlert"
      @isConfirmed="isConfirmed"
    />
    <ErrorModal v-if="showErrModal" :error="error" @isClicked="isClicked" /> -->
    <div class="content__wrapper" v-if="loaded">
      <div class="profile-main-container">
        <!-- =============================================== -->
        <ImageUpload :profileImg="profile_img" />
        <!-- <MenuCategories :username="username" :usermail="usermail" :userphone="userphone" :isSns="isSns" /> -->
        <div class="form__wrapper">
          <PetTypeForm
            :initialType="initialType"
            @petTypeHandler="petTypeHandler"
          />
        </div>
        <div class="form__wrapper">
          <PetSearchForm
            :typeList="typeList"
            :initialBreed="initialBreed"
            @breedHandler="breedHandler"
          />
        </div>
        <GenderForm
          :initialGender="initialGender"
          @genderInputHandler="genderInputHandler"
        />
        <div class="form__wrapper">
          <NeuteredForm
            :initialNeutered="initialNeutered"
            @neuteredHandler="neuteredHandler"
          />
        </div>
        <div class="form__wrapper">
          <NameForm
            :initialName="initialName"
            @nameInputHandler="nameInputHandler"
          />
        </div>
        <!-- <div class="form__wrapper">
          <WeightForm :initialWeight="initialWeight" @weightInputHandler="weightInputHandler" />
        </div> -->
        <div class="form__wrapper">
          <WeightGoalForm
            :initialWeight="initialTargetWeight"
            @weightGoalInputHandler="weightGoalInputHandler"
          />
        </div>
        <div class="form__wrapper">
          <BirthForm
            :initialBirth="initialBirth"
            @birthInputHandler="birthHandler"
          />
        </div>
        <div class="form__wrapper">
          <AdoptionDateForm
            :initialBirth="adoptionDate"
            @adoptionDateHandler="adoptionDateHandler"
            @sameBirthHandler="sameBirthHandler"
          />
        </div>
        <div class="form__wrapper">
          <RegistrationNumberForm
            :initialRegistrationNumber="initialRegistrationNumber"
            @registrationNumberHandler="registrationNumberHandler"
          />
        </div>
        <div class="delete-btn__wrapper">
          <div @click="deleteSubject" class="delete-btn">
            {{ $t("delete_btn") }}
          </div>
        </div>
        <DeleteForm
          v-if="showDeleteForm"
          :subjectId="subjectId"
          @closeDeleteForm="closeDeleteForm"
          @cancel="cancel"
        />
        <div class="btn__wrapper">
          <v-btn
            class="save-btn"
            :disabled="!isAvailableSave"
            elevation="0"
            color="#41D8E6"
            type="submit"
            @click="saveBtnHandler"
            >{{ $t("save") }}</v-btn
          >
        </div>
      </div>
    </div>
    <div class="snackbar">
      <v-snackbar v-model="isSuccess" timeout="2000">{{
        succesContent
      }}</v-snackbar>
      <v-snackbar v-model="delFail" timeout="2000" color="#EE0000">{{
        failContent
      }}</v-snackbar>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import ImageUpload from "@/components/Mypage/ProfileView/ImageUpload.vue";
import PetTypeForm from "@/components/Forms/PetTypeForm.vue";
import PetSearchForm from "@/components/Forms/PetSearchForm.vue";
import GenderForm from "@/components/Forms/GenderForm.vue";
import NeuteredForm from "@/components/Forms/NeuteredForm.vue";
import NameForm from "@/components/Forms/NameForm.vue";
import BirthForm from "@/components/Forms/BirthForm.vue";
// import WeightForm from "@/components/Forms/WeightForm.vue";
import WeightGoalForm from "@/components/Forms/WeightGoalForm.vue";
import AdoptionDateForm from "@/components/Forms/AdoptionDateForm.vue";
import RegistrationNumberForm from "@/components/Forms/RegistrationNumberForm.vue";

import Loading from "@/components/Common/Loading.vue";
import DeleteForm from "./subpages/DeleteSubUser.vue";
import CompleteAlert from "@/components/Common/ConfirmModal.vue";
import ErrorModal from "@/components/Common/ErrorModal.vue";

import API from "@/api/auth/index.js";
import { fetchGetSubjectInfo } from "@/api/user/index";
import { updateSubjectInfo } from "@/api/user/index";

export default {
  name: "Myprofile",
  components: {
    HeaderNav,
    ImageUpload,
    PetTypeForm,
    PetSearchForm,
    GenderForm,
    NeuteredForm,
    NameForm,
    BirthForm,
    // WeightForm,
    WeightGoalForm,
    AdoptionDateForm,
    RegistrationNumberForm,
    DeleteForm,
    Loading,
  },
  data() {
    return {
      isKo: false,
      isCn: false,
      loading: false,
      loaded: false,
      showDeleteForm: false,
      subjectId: null,
      profile_img: "",
      pageName: this.$i18n.t("profile_title"),
      mymenu: false,
      clicked: false,
      isSns: false,
      initialType: null,
      petType: null,
      typeList: [],
      initialBreed: null,
      initialTypeId: null,
      subjectTypeId: null,
      initialName: null,
      name: "",
      validName: false,
      initialNeutered: null,
      neutered: null,
      initialGender: null,
      gender: null,
      validGender: false,
      initialBirth: null,
      birth: null,
      validBirth: false,
      initialAdoptionDate: null,
      adoptionDate: null,
      adoptionDateValid: false,
      initialRegistrationNumber: null,
      registrationNumber: null,
      registrationNumberValid: false,
      height: 0,
      validHeight: false,
      initialWeight: null,
      weight: 0,
      validWeight: false,
      initialTargetWeight: null,
      goalWeight: 0,
      validGoalWeight: false,
      showCompleteAlert: false,
      showErrModal: false,
      isSuccess: false,
      succesContent: this.$i18n.t("successContent"),
      delFail: false,
      failContent: this.$i18n.t("failContent"),
    };
  },
  computed: {
    isAvailableSave() {
      // return true;
      // console.log(this.initialAdoptionDate, this.adoptionDate);
      // return this.adoptionDateValid && this.initialAdoptionDate !== this.adoptionDate;

      return (
        (this.petType !== null &&
          this.initialType !== this.petType &&
          this.initialTypeId !== null &&
          this.initialTypeId !== this.subjectTypeId) ||
        (this.initialTypeId !== null &&
          this.initialTypeId !== this.subjectTypeId) ||
        this.initialNeutered !== this.neutered ||
        (this.gender !== null && this.initialGender !== this.gender) ||
        (this.validName && this.initialName !== this.name) ||
        (this.validGoalWeight &&
          this.initialTargetWeight !== this.goalWeight) ||
        (this.validBirth && this.initialBirth !== this.birth) ||
        (this.adoptionDateValid &&
          this.initialAdoptionDate !== this.adoptionDate &&
          this.adoptionDate !== "null-null-null") ||
        (this.registrationNumberValid &&
          this.initialRegistrationNumber !== this.registrationNumber)
      );
    },
  },

  methods: {
    clickHandler() {
      this.clicked = true;
    },
    getSubjectId() {
      const subjects = JSON.parse(sessionStorage.getItem("subjects"));
      const selectedId = Number(sessionStorage.getItem("selectUser")) || 0;
      const subjectId =
        subjects !== null
          ? subjects[selectedId].id
          : Number(localStorage.getItem("subjectId"));
      return subjectId;
    },
    async loadData() {
      this.loading = true;
      this.loaded = false;
      try {
        const subjectId = this.getSubjectId();
        const { data } = await fetchGetSubjectInfo(subjectId);
        // console.log(data);

        if (data) {
          // console.log(data);
          const petData = data;

          console.log(petData.metadata.registrationNumber);
          console.log(petData.subjectType);
          // console.log(petData.metadata.registrationNumber);
          this.subjectId = petData.id;
          this.initialType = petData.subjectType.category;
          this.petType = petData.subjectType.category;
          this.initialBreed = this.isKo
            ? petData.subjectType.korean
            : this.isCn
            ? petData.subjectType.chinese
            : petData.subjectType.english;
          this.initialTypeId = petData.subjectType.id;
          this.subjectTypeId = petData.subjectType.id;
          this.gender = petData.sex;
          this.initialGender = petData.sex;
          this.initialNeutered = petData.metadata.neutered;
          this.neutered = petData.metadata.neutered;
          this.initialName = petData.nickname;
          this.initialWeight = petData.initialWeight;
          this.initialTargetWeight = petData.targetWeight;
          this.initialBirth = petData.birth;
          this.initialAdoptionDate = petData.metadata.adoptionDate;
          this.adoptionDate = petData.metadata.adoptionDate;
          this.initialRegistrationNumber = petData.metadata.registrationNumber;
          this.registrationNumber = petData.metadata.registrationNumber;
          this.profile_img =
            petData.image ||
            require("@/assets/images_assets/mypage-icon/sub-default.png");
          this.username = petData.nickname;
          console.log(this.initialRegistrationNumber);
        }
        this.loading = false;
        this.loaded = true;
      } catch (error) {
        this.loading = false;
        this.loaded = true;
        console.log(error);
      }
    },
    async getTypeList(type) {
      try {
        const { data, status } = await API.getTypeList(type);
        // console.log(data, status);
        if (status == 200) {
          let petList = data.subjectTypes;

          if (!this.isKo) {
            petList.sort((a, b) => {
              return a.english.localeCompare(b.english);
            });

            petList = petList.map((item) => {
              return {
                ...item,
                english: `${item.english
                  .charAt(0)
                  .toUpperCase()}${item.english.slice(1)}`,
              };
            });
          }

          console.log(petList);

          this.typeList = petList;
        }
      } catch (e) {
        console.error(e);
      }
    },
    async updatePetInfo(petData) {
      try {
        const subjectId = this.getSubjectId();
        const { data, status } = await updateSubjectInfo(subjectId, petData);
        // console.log(data, status);
        if (status === 200) {
          this.isSuccess = true;
          this.loadData();
        }
      } catch (e) {
        console.log(e);
      }
    },
    saveBtnHandler() {
      // (this.validName && this.initialName !== this.name) ||
      // (this.validWeight && this.initialWeight !== this.weight) ||
      // (this.validGoalWeight && this.initialTargetWeight !== this.goalWeight) ||
      // (this.validBirth && this.initialBirth !== this.birth) ||
      // (this.adoptionDateValid && this.initialAdoptionDate !== this.adoptionDate) ||
      // (this.registrationNumberValid && this.initialRegistrationNumber !== this.registrationNumber)
      const petData = {};
      if (
        this.initialTypeId !== null &&
        this.initialTypeId !== this.subjectTypeId
      ) {
        petData.subjectTypeId = this.subjectTypeId;
      }
      if (this.initialNeutered !== this.neutered) {
        petData.neutered = this.neutered;
      }
      if (this.gender !== null && this.initialGender !== this.gender) {
        petData.sex = this.gender;
      }
      if (this.validName && this.initialName !== this.name) {
        petData.nickname = this.name;
      }
      if (
        this.validGoalWeight &&
        this.initialTargetWeight !== this.goalWeight
      ) {
        petData.targetWeight = Number(this.goalWeight);
      }
      if (this.validBirth && this.initialBirth !== this.birth) {
        petData.birth = this.birth;
      }
      if (
        this.adoptionDateValid &&
        this.initialAdoptionDate !== this.adoptionDate
      ) {
        petData.adoptionDate = this.adoptionDate;
      }
      if (
        this.registrationNumberValid &&
        this.initialRegistrationNumber !== this.registrationNumber
      ) {
        petData.registrationNumber = this.registrationNumber;
      }
      // console.log(petData);
      this.updatePetInfo(petData);
    },

    async deleteSubject() {
      this.showDeleteForm = true;
      // const {data, status} = await API.deleteSubject(data);
    },

    closeDeleteForm() {
      this.showDeleteForm = false;
    },

    cancel() {
      this.showDeleteForm = false;
    },

    // emit
    petTypeHandler(type) {
      // console.log(type);
      this.petType = type;
      this.getTypeList(type);
    },
    breedHandler(breedObj) {
      // console.log(breedObj);
      this.subjectTypeId = Number(breedObj.id);
    },
    genderInputHandler(gender) {
      // console.log(gender);
      this.gender = gender;
    },
    neuteredHandler(neutered) {
      // console.log(neutered);
      this.neutered = neutered;
    },
    nameInputHandler(inputName) {
      // console.log("name input", inputName);
      this.name = inputName.name;
      this.validName = inputName.valid;
    },
    birthHandler(fromChild) {
      // console.log(fromChild);
      this.birth = fromChild.birth;
      this.validBirth = fromChild.valid;
    },
    adoptionDateHandler(adoptionDateObj) {
      // console.log(adoptionDateObj);
      console.log("hi adoptionDateHandler ");
      console.log(adoptionDateObj);
      this.adoptionDate = adoptionDateObj.adoptionDate;
      this.adoptionDateValid = adoptionDateObj.valid;
    },
    sameBirthHandler(isSame) {
      this.adoptionDate = isSame ? this.birth : null;
    },
    registrationNumberHandler(registrationNumberObj) {
      // console.log(registrationNumberObj);
      this.registrationNumber = registrationNumberObj.registrationNumber;
      this.registrationNumberValid = registrationNumberObj.valid;
    },
    weightInputHandler(inputWeight) {
      // console.log(inputWeight);
      this.weight = inputWeight.weight;
      this.validWeight = inputWeight.valid;
    },
    weightGoalInputHandler(inputGoalWeight) {
      // console.log(inputGoalWeight);
      // console.log(inputGoalWeight.goal);
      this.goalWeight = inputGoalWeight.goal;
      this.validGoalWeight = inputGoalWeight.valid;
    },
  },
  mounted() {
    // console.log("this is profile edit page");
    this.isKo = this.$i18n.locale.includes("ko");
    this.isCn = this.$i18n.locale.includes("cn");
    this.loadData();
  },
};
</script>

<style lang="scss" scoped>
.profile-main-container {
  width: 100%;
  /* display: flex;
  flex-direction: column; */
  /* padding: 0 30px; */
}

.content__wrapper {
  width: 100%;
  height: calc(100vh - 110px);
  padding: 0 30px 100px;
  overflow: auto;
}
.form__wrapper {
  min-height: 80px;
  margin: 25px 0;
}

.delete-btn__wrapper {
  padding: 20px 0 70px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.delete-btn {
  padding: 0 5px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 0.5px solid #a7a7a7;
  color: #a7a7a7;
}

.btn__wrapper {
  width: 100%;
  position: fixed;
  /* bottom: 5vh; */
  bottom: 0;
  height: calc(5vh + 80px);
  left: 0;
  padding: 20px 30px 0;
  display: flex;
  align-items: baseline;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    #ffffff 20.83%,
    #ffffff 100%
  );
}

.save-btn {
  letter-spacing: -0.03em;
  height: 50px !important;
  position: relative;
  z-index: 2;
  max-width: 340px;
  width: 100%;
  border-radius: 10px;
  line-height: 50px;
  font-size: 20px !important;
  font-weight: bold !important;
  color: #ffffff !important;
  margin: 0 auto;
}

.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  background-color: #41d8e6 !important;
}

.theme--light.v-btn.v-btn--disabled {
  color: #c9f4f8 !important;
  opacity: 1;
}

/* snackbar custom */
.snackbar {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  display: flex;
  flex-direction: column;
  gap: 50px;
}

::v-deep .v-snack__wrapper {
  min-width: 220px;
  width: 220px !important;
  height: 55px;
  max-width: 220px;
  margin: 0 !important;
}

::v-deep .v-sheet.v-snack__wrapper:not(.v-sheet--outlined) {
  box-shadow: none;
}

::v-deep .v-snack__wrapper.theme--dark {
  background-color: #000000;
  opacity: 0.8 !important;
}

::v-deep .v-sheet.v-snack__wrapper {
  border-radius: 5px;
}

::v-deep .v-snack__content {
  padding: 0 0 3px 10px !important;
  line-height: 29px;
  display: flex;
  justify-content: center;
  text-align: center;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: -0.03em;
  height: 100%;
  align-items: center;
}
</style>
