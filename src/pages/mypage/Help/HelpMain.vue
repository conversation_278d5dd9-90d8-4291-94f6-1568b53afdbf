<template>
  <div>
    <HeaderNav :pageName="pageName" />
    <div class="help_container">
      <div class="menu__wrapper">
        <router-link to="/help/faq">
          <div class="menu-card">
            <div class="menu-card-header">
              <div class="menu-card-title">{{ $t("faq") }}</div>
              <div class="icon__wrapper">
                <img src="@/assets/images/bottom-arrow.png" alt="right arrow" />
              </div>
            </div>
          </div>
        </router-link>
        <div class="menu-card">
          <div v-if="isKo" @click="goKakaoChannel" class="menu-card-header">
            <div class="menu-card-title">{{ $t("kakao_inquiry") }}</div>
            <div class="icon__wrapper">
              <img src="@/assets/images/bottom-arrow.png" alt="right arrow" />
            </div>
          </div>
          <div v-else class="menu-card-header" @click="handleEmailClick">
            <div class="menu-card-title">{{ $t("email_inquiry_title") }}</div>
            <span class="email-text">{{ COMPANY_EMAIL }}</span>
          </div>
          <div class="underline"></div>
        </div>
        <div class="menu-card">
          <div class="menu-card-header">
            <div class="menu-card-title">{{ $t("terms_info") }}</div>
          </div>
        </div>
        <div class="terms-card">
          <div v-for="(terms, idx) in terms" :key="idx">
            <router-link :to="{ path: terms.path }" class="menu-card-header">
              <div class="menu-card-content">{{ terms.title }}</div>
              <div class="icon__wrapper">
                <img src="@/assets/images/bottom-arrow.png" alt="right arrow" />
              </div>
            </router-link>
          </div>
        </div>
      </div>
    </div>
    <div class="snackbar">
      <v-snackbar
        v-model="snackbarVisible"
        :timeout="2000"
        :color="snackbarColor"
      >
        {{ snackbarMessage }}
      </v-snackbar>
    </div>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/SubHeaderNav.vue";
import { useClipboard } from "@/hooks/useClipboard";

export default {
  name: "HelpMain",
  components: { HeaderNav },
  data() {
    return {
      snackbarVisible: false,
      snackbarMessage: "",
      snackbarColor: "",
      COMPANY_EMAIL: "<EMAIL>",
      pageName: this.$i18n.t("category_customer_service"),
      isKo: this.$i18n.locale.includes("ko") ?? false,
      successText: this.$i18n.t("copy_success"),
      failText: this.$i18n.t("copy_fail"),
      terms: [
        {
          title: this.$i18n.t("service_terms"),
          path: "/help/terms/service",
        },
        {
          title: this.$i18n.t("privacy_terms"),
          path: "/help/terms/privacy",
        },
        {
          title: this.$i18n.t("marketing_terms"),
          path: "/help/terms/marketing",
        },
        {
          title: this.$i18n.t("sensitive_terms"),
          path: "/help/terms/sensitive",
        },
      ],
    };
  },
  computed: {},
  methods: {
    goKakaoChannel() {
      /*global Webview*/
      /*eslint no-undef: "error"*/
      // const message = {
      //   action: "goKakaoChannel",
      //   url: "https://pf.kakao.com/_bxbQrs/chat",
      // };
      // Webview.goKakaoChannel(message);
      // const recipient = "<EMAIL>";
      // const subject = this.$i18n.t("email_inquiry");
      // const body = "아래에 문의 내용을 입력해주세요.";

      // const mailtoLink = `mailto:${recipient}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      // window.location.href = mailtoLink;

      const message = "https://pf.kakao.com/_bxbQrs/chat";
      Webview.openUrl(message);
    },

    handleEmailClick(e) {
      const text = document.querySelector(".email-text").innerHTML;
      const { getClipboard } = useClipboard();

      e?.preventDefault();

      getClipboard(text, {
        onSuccess: () => {
          this.snackbarMessage = this.successText;
          this.snackbarColor = ""; // 성공 시 원하는 색상 지정
          this.snackbarVisible = true;
        },
        onError: (err) => {
          console.error(err);
          this.snackbarMessage = this.failText;
          this.snackbarColor = "#EE0000";
          this.snackbarVisible = true;
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.email-text {
  font-family: GilroyMedium !important;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: right;
  vertical-align: middle;
  color: #646464;
}

.help_container {
  width: 100%;
  text-align: left;
}

.menu__wrapper {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  padding: 40px 30px;
}

.menu-card {
  width: 100%;
}

.terms-card {
  margin: 20px 0;
}

.menu-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.menu-card-title {
  font-size: 18px;
  font-weight: 500;
  color: #000000;
}

.icon__wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 14px;
    transform: rotate(-90deg);
  }
}

.underline {
  border-bottom: 0.5px solid #a7a7a7;
  margin-bottom: 20px;
}

.menu-card-content {
  padding-left: 10px;
  color: #646464;
  font-size: 16px;
  line-height: 23px;
  /* margin-bottom: 10px; */
}

.snackbar {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 10px);
  z-index: 10000;
}

::v-deep .v-snack__wrapper {
  min-width: 220px;
  width: 220px !important;
  height: 55px;
  max-width: 220px;
}
::v-deep .v-sheet.v-snack__wrapper:not(.v-sheet--outlined) {
  box-shadow: none;
}

::v-deep .v-snack__wrapper.theme--dark {
  background-color: #000000;
  opacity: 0.8 !important;
  margin: 0;
}

::v-deep .v-sheet.v-snack__wrapper {
  border-radius: 5px;
}

::v-deep .v-snack__content {
  padding: 0 0 3px 10px !important;
  // width: 210px !important;
  line-height: 29px;
  display: flex;
  justify-content: center;
  text-align: center;
  font-size: 22px;
  font-weight: 500;
  letter-spacing: -0.03em;
}
</style>
