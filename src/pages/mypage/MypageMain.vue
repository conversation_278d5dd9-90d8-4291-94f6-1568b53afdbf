<template>
  <div>
    <!-- cym-color-zone -->
    <ErrModal
      v-if="noSubjects"
      :contents="this.$i18n.t('no_pet_text')"
      :btnText="this.$i18n.t('adding_pet')"
      @showModal="addingPet"
    />
    <ErrModal
      v-if="isFulled"
      :contents="this.$i18n.t('adding_impossible')"
      :btnText="this.$i18n.t('confirm_btn')"
      @showModal="showModal"
    />
    <SelectModal
      v-if="showSelectModal"
      :userName="selectedName"
      @selectModal="selectModal"
      @switchUser="switchUser"
    />
    <div class="cym-color-zone">
      <HeaderNav />
      <div class="profile__wrapper" ref="profileWrapper">
        <div class="scroll-zone">
          <div
            class="img-box"
            v-for="(profile, index) in profiles"
            :key="index"
            @click="
              profile.name.length === 0 ? checkSubUser() : isSelected(index)
            "
          >
            <div
              class="img__wrapper"
              :class="{ selected: index === isSelectUser }"
            >
              <img :src="profile.img" alt="Profile Image" />
            </div>
            <div v-if="profile.name.length === 0" class="name__wrapper">
              &nbsp;
            </div>
            <div v-else class="name__wrapper">
              {{ profile.name }}
            </div>
          </div>
          <!-- <div class="img__wrapper"><img src="@/assets/images_assets/mypage-icon/sub-default.png" /></div>
          <div class="img__wrapper"><img src="@/assets/images_assets/mypage-icon/sub-default.png" /></div>
          <div class="img__wrapper"><img src="@/assets/images_assets/mypage-icon/sub-default.png" /></div>
          <div class="img__wrapper"><img src="@/assets/images_assets/mypage-icon/sub-default.png" /></div> -->
        </div>
      </div>
      <!-- <div v-if="isSelectUser === 0" class="active-profile-animation"></div> -->
      <!-- <div class="profile_wrapper">
        <div :class="isSelectUser === 0 ? 'active-profile' : 'profile-img'" @click="isSelected(0)">
          <img :src="profile_img" alt="user profile" class="profile-img-tg" @error="replaceImage" />
        </div>
      </div>
      <div class="profile_name_wrapper">
        <div class="profile-name">{{ username }}</div>
      </div> -->

      <!-- my solution area -->
      <!-- <div v-if="isSelectUser === 1"></div>
      <div v-if="isSelectUser === 2"></div>
      <div v-if="isSelectUser === 3"></div> -->
      <div class="user-edit-section">
        <!-- <div class="sub-profile_wrapper">
          <div class="sub-profile-img_box" v-for="(sub, idx) in subUser" :key="idx" @click="isSelected(idx + 1)">
            <div :class="isSelectUser === idx + 1 ? 'active-sub' : 'sub-profile-img_wrapper'">
              <img :src="sub.image" />
            </div>
            <div class="sub-user-name">{{ sub.name.length > 5 ? `${sub.name.slice(0, 4)}...` : sub.name }}</div>
          </div>
        </div> -->
        <div class="user-edit-wrapper">
          <!-- <router-link class="edit-btn__wrapper" to="/profile"> -->
          <div class="edit-btn__wrapper" @click="checkSelcetUser">
            <v-icon class="edit-icon">$edit</v-icon>
            <div class="btn-title">{{ $t("profile_title") }}</div>
          </div>
          <!-- </router-link> -->
          <div class="edit-btn__wrapper" @click="checkSubUser">
            <v-icon class="edit-icon">$user_plus</v-icon>
            <div class="btn-title">{{ $t("add_pet") }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- menu-zone -->
    <div class="menu-container__wrapper">
      <div class="menu-container">
        <!-- =================================================================== -->
        <!-- 🚨 Mypage category-guide -->
        <div class="mymenu-wrapper" v-for="(item, i) in mypageMenus" :key="i">
          <div class="menu-icon">
            <img :src="item.img" alt="" id="guide-icon" />
          </div>
          <router-link :to="{ path: item.path }" class="menu-title">
            {{ item.title }}
          </router-link>
        </div>
        <div class="underline-wrapper">
          <div class="underline"></div>
        </div>

        <!-- =================================================================== -->
        <!-- 🚨 Mypage category-guide -->
        <!-- <div class="mymenu-wrapper">
        <div class="menu-icon">
          <img src="@/assets/images/mypage-icon/push_icon.png" alt="" />
        </div>
        <div class="menu-title">알림설정</div>
      </div> -->

        <!-- =================================================================== -->
        <!-- 🚨 Mypage category-about-us -->
        <div class="company-logo-wrapper">
          <div class="logo-icon">
            <img src="@/assets/images/mypage-icon/yellosis_icon.png" />
          </div>
          <div class="company-name" @click="goCym702">
            {{ $t("category_about_company") }}
          </div>
        </div>
      </div>
    </div>
    <navigation :path="path"></navigation>
  </div>
</template>

<script>
import HeaderNav from "@/components/Mypage/modules/HeaderNav.vue";
import { fetchGetUserInfo } from "@/api/user/index";
import ErrModal from "./ErrModal.vue";
import SelectModal from "./SelectModal.vue";

export default {
  name: "MypageMain",
  components: {
    HeaderNav,
    ErrModal,
    SelectModal,
  },
  data() {
    return {
      path: "/mypage",
      profiles: [
        // { img: require("@/assets/images/plus.png"), name: "" },
        // { img: require("@/assets/images/plus.png"), name: "" },
        // { img: require("@/assets/images/plus.png"), name: "" },
        // { img: require("@/assets/images/plus.png"), name: "" },
      ],
      basicImg: require("@/assets/images_assets/mypage-icon/sub-default.png"),
      selectedIndex: 0,
      mypageMenus: [
        {
          img: require("@/assets/images/mypage-icon/guide_icon.png"),
          title: this.$i18n.t("category_guide"),
          path: "/mypage/guide",
        },
        // {
        //   img: require("@/assets/images/mypage-icon/push_icon.png"),
        //   title: this.$i18n.t("category_alert"),
        //   path: "/mypage/examalert",
        // },
        {
          img: require("@/assets/images/mypage-icon/notice_icon.png"),
          title: this.$i18n.t("category_notice"),
          path: "/mypage/notice",
        },
        {
          img: require("@/assets/images/mypage-icon/help_icon.png"),
          title: this.$i18n.t("category_customer_service"),
          path: "/help",
        },
      ],
      username: "",
      userphone: "",
      profile_img: "",
      selectedName: "",
      isPlural: false,
      isFulled: false,
      showSelectModal: false,
      MAX_PROFILE_LENGTH: 10,
      subUser: [
        // {
        //   id: 1,
        //   subId: 1,
        //   image: require("@/assets/images_assets/mypage-icon/sub-default.png"),
        //   name: "",
        // },
        // {
        //   id: 2,
        //   subId: 2,
        //   image: require("@/assets/images_assets/mypage-icon/sub-default.png"),
        //   name: "",
        // },
        // {
        //   id: 3,
        //   subId: 3,
        //   image: require("@/assets/images_assets/mypage-icon/sub-default.png"),
        //   name: "",
        // },
      ],
      isSubUser: true,
      isSelectUser: 0,
      choiceId: 0,
      profileLength: 0,
      isMake: true,
      // noSubjects: false,
    };
  },

  computed: {
    noSubjects() {
      return this.$store.state.noSubjects;
    },
  },

  watch: {
    isSelectUser(newVal) {
      // console.log(newVal);
    },
  },

  mounted() {
    this.loadData();
    // sessionStorage에 저장된 값으로 설정
    // const selectUser = this.$store.state.selectUser > 0 ? this.$store.state.subUserId : this.$store.state.selectUser;
    // console.log(JSON.parse(sessionStorage.getItem("subjects")).length);
    // this.noSubjects = JSON.parse(sessionStorage.getItem("subjects")).length === 0;
    this.isSelectUser = Number(sessionStorage.getItem("selectUser")) || 0;
  },

  methods: {
    async loadData() {
      try {
        const { data } = await fetchGetUserInfo();

        this.profileLength = 0;
        // console.log(data.subjects);
        if (data.length !== 0) {
          sessionStorage.setItem("subjects", JSON.stringify(data));
          const subjectsData = [];
          this.profiles = [];
          this.isMake = data.length < this.MAX_PROFILE_LENGTH ? true : false;

          data.map((user, idx) => {
            subjectsData.push(user);
            this.profiles.push({
              img:
                user.image ||
                require("@/assets/images_assets/mypage-icon/sub-default.png"),
              name: user.nickname,
            });
            // this.profiles[idx].img = user.image || require("@/assets/images_assets/mypage-icon/sub-default.png");
            // this.profiles[idx].name = user.nickname;
          });

          if (data.length < 4) {
            for (let i = 0; i < 4 - data.length; i++) {
              this.profiles.push({
                img: require("@/assets/images/plus.png"),
                name: "",
              });
            }
          } else if (data.length <= 9) {
            this.profiles.push({
              img: require("@/assets/images/plus.png"),
              name: "",
            });
          }

          subjectsData.shift();

          // console.log("subject data", subjectsData);

          const mainUserData = data[0];
          this.username = mainUserData.nickname;
          this.$store.commit("getUsername", mainUserData.nickname);
          this.profile_img =
            mainUserData.image ||
            require("@/assets/images/mypage-icon/profile.png");
          this.$store.commit("getUserImage", mainUserData.image);
          this.userPhone = mainUserData.phone;
          this.$store.commit("getUserPhone", mainUserData.phone);

          subjectsData.map((sub) => {
            // console.log(sub);
            this.subUser.push({
              img:
                sub.image !== null
                  ? sub.image
                  : require("@/assets/images_assets/mypage-icon/sub-default.png"),
              name: sub.nickname || "",
              subId: sub.id || "",
            });
          });
          // console.log("+++++", this.subUser);
          this.$store.commit("setNoSubjects", false);
        } else {
          this.$store.commit("setNoSubjects", true);
        }
      } catch (error) {
        console.log(error);
      }
    },
    isSelected(idx) {
      // console.log(idx);
      console.log("isSelected");
      if (idx > 0) {
        this.choiceId = idx;
        if (this.subUser[idx - 1].name !== "") {
          if (this.isSelectUser !== idx) {
            this.selectedName = this.subUser[idx - 1].name;
            this.showSelectModal = true;
          }
        } else {
          this.showSelectModal = false;
        }
      } else if (idx === 0) {
        if (this.isSelectUser !== idx) {
          this.choiceId = idx;
          this.selectedName = this.username;
          this.showSelectModal = true;
        }
      }
    },
    selectModal(fromChild) {
      this.showSelectModal = false;
    },
    switchUser(fromChild) {
      // console.log(fromChild);
      this.isSelectUser = this.choiceId;
      sessionStorage.setItem("selectUser", this.choiceId);
      // this.$store.commit("setSelectUser", this.choiceId);
      // localStorage.setItem("selectUser", this.choiceId);

      // if (this.choiceId > 0) {
      //   // console.log(this.subUser[this.choiceId - 1].subId);
      //   this.$store.commit("setSubUserId", this.subUser[this.choiceId - 1].subId);
      //   localStorage.setItem("selectUser", this.subUser[this.choiceId - 1].subId);
      // }
      this.loadData();
      this.showSelectModal = false;
    },
    replaceImage(e) {
      e.target.src = require("@/assets/images/mypage-icon/profile.png");
    },
    /*global Webview*/
    /*eslint no-undef: "error"*/

    goCym702() {
      // 회사 소개 페이지로 이동.
      const message = "https://yellosis.com/company.html";
      Webview.openUrl(message);
    },
    addingPet() {
      this.$router.push({ name: "SubUserAdd" });
    },

    showModal(fromChild) {
      // console.log(fromChild);
      this.isFulled = fromChild;
    },
    checkSelcetUser() {
      this.$router.push({ name: "Myprofile" });
    },
    checkSubUser() {
      console.log(this.profiles);
      if (this.profiles.length - 1 >= 10 || !this.isMake) {
        console.log("init");
        this.isFulled = true;
      } else {
        this.$router.push({ name: "SubUserAdd" });
        this.isFulled = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.bg-main-color {
  background-color: #c9f4f8 !important;
}

.cym-color-zone {
  background-color: #c9f4f8;
  // padding: 0 30px;
  box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
}

.profile__wrapper {
  width: 100%;
  overflow-x: scroll;
  left: 0;
  padding: 0 35%;
  display: flex;
  height: 240px;
}

.scroll-zone {
  display: flex;
  align-items: center;
  gap: 0 30px;
  white-space: nowrap;
  padding-bottom: 50px;
}

.img-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: end;
}

.name__wrapper {
  font-weight: 500;
  font-size: 20px;
  margin-top: 10px;
}
// .img__wrapper {
//   width: 130px; // 140px
//   height: 130px; // 140px
//   border-radius: 50%;
//   background-color: #fff;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   position: relative;
//   img {
//     width: 100%;
//     height: 100%;
//     object-fit: cover;
//     border-radius: 50%;
//     border: 5px solid #c9f4f8;
//   }
// }

.img__wrapper {
  width: 100px;
  height: 100px;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  overflow: hidden;
  border: 5px solid transparent;
  transition: border-color 0.2s;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    // border: 5px solid #c9f4f8;
  }
}

.img__wrapper.selected {
  width: 150px; /* Selected state size */
  height: 150px; /* Selected state size */
  border-color: #41d8e6;
}

.profile_wrapper {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
}
.profile_name_wrapper {
  padding: 0 30px;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-top: 10px;

  .profile-name {
    color: #000000;
    font-weight: 500;
    font-size: 22px;
  }
}

.profile-img {
  width: 140px;
  height: 140px;
  border-radius: 50%;
  background-color: #fff;
  position: relative;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #c9f4f8;
  }
}

.active-profile {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: #fff;
  position: relative;
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    border: 5px solid #41d8e6;
  }
}

.menu-container__wrapper {
  padding: 25px 30px 90px 30px;
  height: 100%;
}

.user-edit-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px 30px 25px 30px;
}

.user-edit-wrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 30px;
}

.edit-btn__wrapper {
  display: flex;
  height: 29px;
}

.edit-icon {
  margin-top: 2px;
}

.go-solution-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.mysolution-title {
  margin-left: 10px;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 0px;
  letter-spacing: -0.03em;
}

.solution-icon-img {
  width: 10px;
  margin-top: 6px;
  img {
    width: 100%;
    object-fit: contain;
  }
}

.btn-title {
  font-size: 18px;
  font-weight: 400;
  color: #646464;
  letter-spacing: -0.03em;
}
.sub-profile_wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 50px;
  padding: 0 30px;
}

.sub-profile-img_box {
  // z-index: 2;
  background: #fff;
  width: 70px;
  max-width: 80px;
  height: 70px;
  border-radius: 100%;
}

.sub-profile-img_wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    z-index: 3;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid #c9f4f8;
  }
}

.active-sub {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    z-index: 3;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    object-fit: cover;
    border: 5px solid #41d8e6;
  }
}

.sub-user-name {
  font-size: 14px;
  font-weight: 500;
  width: 70px;
  display: flex;
  justify-content: center;
  letter-spacing: -0.03em;
  line-height: 14px;
  padding-top: 7px;
}

.sub-profile-img {
  width: 100%;
  height: 100%;
  margin: 3px 0 0 5px;
}

.right-icon {
  margin-left: 3px;
}

.solution_section {
  padding: 0px 30px 30px 30px;
}

.my-solution-box {
  width: 100%;
  height: 70px;
  background-color: #f8f8f8;
  border-radius: 10px;
  margin-top: 13px;
  line-height: 70px;
  color: #41d8e6;
  font-size: 13px;
  font-weight: 500;
}
.mymenu-wrapper {
  padding: 0px 30px 25px 30px;
  display: flex;
  align-items: center;

  a {
    text-decoration: none;
    color: #000;
  }
}

.menu-icon {
  width: 22px;
  display: flex;
  align-items: center;
  img {
    width: 100%;
    object-fit: contain;
  }
}
#guide-icon {
  width: 18px;
  margin-right: 4px;
}

.menu-title {
  font-style: normal;
  font-weight: 500 !important;
  font-size: 20px;
  line-height: 22px;
  letter-spacing: -0.03em;
  margin-left: 10px;
}

// .menu-container {
//   border-bottom: 1px solid #a7a7a7;
// }

.underline-wrapper {
  padding: 0 30px;
}

.underline {
  border-top: 0.5px solid #a7a7a7;
}
.company-logo-wrapper {
  display: flex;
  padding: 25px 30px;
  align-items: center;
}

.logo-icon {
  width: 20px;
}

.logo-icon > img {
  width: 100%;
  object-fit: contain;
}

.company-name {
  font-style: normal;
  font-weight: 500;
  font-size: 20px;
  line-height: 23px;
  letter-spacing: -0.03em;
  margin-left: 14px !important;
}

// active profile animation
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
  }
}

.active-profile-animation {
  z-index: 0;
  position: absolute;
  margin: 0 auto;
  width: 140px;
  height: 140px;
  border: 5px solid transparent;
  border-radius: 50%;
  background-image: linear-gradient(#fff, #fff),
    linear-gradient(180deg, #41d8e6 0%, #cdf5f9 85%, #c9f4f8 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  -webkit-transition: all 1s ease-in;
  -webkit-animation-name: rotate;
  -webkit-animation-duration: 1.5s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  // transition: all 1s ease-in;
  animation-name: rotate;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
.active-sub1 {
  z-index: 0;
  position: absolute;
  margin: 0 auto;
  width: 80px;
  height: 80px;
  border: 5px solid transparent;
  border-radius: 50%;
  left: 55px;
  background-image: linear-gradient(#fff, #fff),
    linear-gradient(180deg, #41d8e6 0%, #cdf5f9 85%, #c9f4f8 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  // -webkit-transition: all 1s ease-in;
  -webkit-animation-name: rotate;
  -webkit-animation-duration: 1.5s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  // transition: all 1s ease-in;
  animation-name: rotate;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
.active-sub2 {
  z-index: 0;
  position: absolute;
  margin: 0 auto;
  width: 80px;
  height: 80px;
  border: 5px solid transparent;
  border-radius: 50%;
  left: 50%;
  margin-left: -40px;
  background-image: linear-gradient(#fff, #fff),
    linear-gradient(180deg, #41d8e6 0%, #cdf5f9 85%, #c9f4f8 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  // -webkit-transition: all 1s ease-in;
  -webkit-animation-name: rotate;
  -webkit-animation-duration: 1.5s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  // transition: all 1s ease-in;
  animation-name: rotate;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
.active-sub3 {
  z-index: 0;
  position: absolute;
  margin: 0 auto;
  width: 80px;
  height: 80px;
  border: 5px solid transparent;
  border-radius: 50%;
  right: 55px;
  background-image: linear-gradient(#fff, #fff),
    linear-gradient(180deg, #41d8e6 0%, #cdf5f9 85%, #c9f4f8 100%);
  background-origin: border-box;
  background-clip: content-box, border-box;
  // -webkit-transition: all 1s ease-in;
  -webkit-animation-name: rotate;
  -webkit-animation-duration: 1.5s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  // transition: all 1s ease-in;
  animation-name: rotate;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-timing-function: linear;
}
</style>
