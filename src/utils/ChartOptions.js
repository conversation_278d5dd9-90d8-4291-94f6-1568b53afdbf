import i18n from "../i18n";

function chartStrokeOptions(clickedValue) {
  return clickedValue === "recent" ? "straight" : "smooth";
}

function yaxisTitleText(urineTestItemType) {
  if (urineTestItemType === "blood") return "RBC/µL";
  if (urineTestItemType === "leukocytes") return "WBC/µL";

  if (
    urineTestItemType === "ph" ||
    urineTestItemType === "nitrite" ||
    urineTestItemType === "sg"
  )
    return "";
  return "mg/dL";
}

/** y축 개수 */
function yaxisMaxValue(urineTestItemType) {
  // console.log(urineTestItemType);
  if (
    urineTestItemType === "protein" ||
    urineTestItemType === "glucose" ||
    urineTestItemType === "sg"
  ) {
    return 6;
  }
  if (
    urineTestItemType === "blood" ||
    urineTestItemType === "leukocytes" ||
    urineTestItemType === "bilirubin"
  ) {
    return 4;
  }
  if (urineTestItemType === "nitrite") {
    return 2;
  }
  return 5;
}

/** y축 내용 */
function yaxisLabelsValue(type, value) {
  // console.log(type, value);
  switch (type) {
    case "blood":
      if (value === 1) return "0";
      if (value === 2) return "10";
      if (value === 3) return "50";
      if (value === 4) return "250";
      else return "";
    case "protein":
      if (value === 2) return "10";
      else if (value === 3) return "30";
      else if (value === 4) return "100";
      else if (value === 5) return "300";
      else if (value === 6) return "1000";
      else return "";
    case "glucose":
      if (value === 2) return "100";
      else if (value === 3) return "250";
      else if (value === 4) return "500";
      else if (value === 5) return "1000";
      else if (value === 6) return "2000";
      else return "";
    case "ph":
      if (value === 1) return "pH 5";
      else if (value === 2) return "pH 6";
      else if (value === 3) return "pH 7";
      else if (value === 4) return "pH 8";
      else if (value === 5) return "pH 9";
      else return "";
    case "ketone":
      if (value === 3) return "10";
      else if (value === 4) return "50";
      else if (value === 5) return "100";
      else return "";
    case "leukocytes": {
      const scores = ["0", "25", "75", "500"];

      if (value <= 0 || value >= 5) {
        return "";
      }

      return scores[value - 1];
    }
    case "bilirubin": {
      const scores = ["0", "0.5", "1.0", "3.0"];

      return scores[value - 1];
    }
    case "urobilinogen": {
      const scores = ["0.1", "1", "4", "8", "12"];

      return scores[value - 1];
    }
    case "nitrite": {
      if (value === 1) {
        return i18n.t("negative");
      }

      return i18n.t("positive");
    }
    case "sg": {
      const scores = ["1.000", "1.005", "1.010", "1.020", "1.025", "1.030"];

      return scores[value - 1];
    }
    default:
      return "";
  }
}

export default {
  apexchartOptions() {
    const chartOptions = {
      offsetX: 0,
      animations: {
        enabled: false,
      },
      zoom: {
        enabled: false,
      },
      dropShadow: {
        enabled: true,
        color: "#A7A7A7",
        top: 5,
        left: 0,
        blur: 6,
        opacity: 0.15,
      },
      toolbar: {
        show: false,
      },
      states: {
        active: {
          allowMultipleDataPointsSelection: true,
        },
      },
    };
    return chartOptions;
  },

  strokeOptions: (clickedValue) => {
    const strokeOptions = {
      width: 2.5,
      lineCap: "butt",
      curve: chartStrokeOptions(clickedValue),
    };
    return strokeOptions;
  },

  xaxisOptions(chartLables) {
    const min = chartLables[0];
    const max = chartLables[chartLables.length];
    const len = chartLables.length;
    const xaxisOptions = {
      tooltip: {
        enabled: false,
      },
      axisBorder: {
        show: false,
      },
      min: min,
      max: max,
      tickAmount: len > 12 ? 7 : 19,
      categories: chartLables,
      // overwriteCategories: chartLables.slice(0, 6),
      labels: {
        rotate: 0,
        rotateAlways: false,
        hideOverlappingLabels: true,
        style: {
          colors: ["#646464"],
          fontSize: "14px",
          cssClass: "apexcharts-yaxis-label",
        },
        // formatter: function(value) {
        //   if (chartLabels.length > 5) {
        //     if (value % 50 === 0) {
        //       return chartLabels[value - 1];
        //     } else {
        //       return "";
        //     }
        //   }
        //   return chartLabels[value - 1];
        // }
      },
    };
    return xaxisOptions;
  },

  yaxisOptions(urineTestItemType) {
    const yaxisOptions = {
      logBase: 1,
      title: {
        text: yaxisTitleText(urineTestItemType),
        offsetY: 80,
        offsetX: urineTestItemType === "urobilinogen" ? 8 : 12,
        style: {
          color: "#a7a7a7",
          fontSize: "10px",
          fontWeight: 500,
        },
      },
      min: 1,
      max: yaxisMaxValue(urineTestItemType),
      tickAmount: yaxisMaxValue(urineTestItemType) - 1,
      labels: {
        style: {
          colors: ["#646464"],
          fontSize: "16px",
          letterSpacing: "-0.08em",
          cssClass: "apexcharts-yaxis-label",
        },
        // offsetX: 3,
        // offsetY: 0,

        formatter: (value) => {
          if (value === 1) return yaxisLabelsValue(urineTestItemType, value);
          if (value === 2) return yaxisLabelsValue(urineTestItemType, value);
          if (value === 3) return yaxisLabelsValue(urineTestItemType, value);
          if (value === 4) return yaxisLabelsValue(urineTestItemType, value);
          if (value === 5) return yaxisLabelsValue(urineTestItemType, value);
          if (value === 6) return yaxisLabelsValue(urineTestItemType, value);
        },
      },
    };

    const cym702YaxisOptions = {
      min: 0,
      max: 100,
      tickAmount: 4,
    };

    if (urineTestItemType === "cym702") {
      return cym702YaxisOptions;
    } else {
      return yaxisOptions;
    }
  },

  markersOptions(count) {
    const markersOptions = {
      strokeColor: ["#A7A7A7"],
      colors: ["#fff"],
      // size: count === "recent" ? 5 : 0,
      hover: {
        size: 5.5,
      },
    };
    return markersOptions;
  },

  gridOptions() {
    const gridOptions = {
      yaxis: {
        lines: {
          show: true,
        },
      },
      strokeDashArray: 2,
    };
    return gridOptions;
  },
};
