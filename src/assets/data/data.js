import i18n from "../../i18n.js";
// detail info: modal contents -> _modal
// txt -> _level

const explainations = {
  blood: i18n.t("blood_info"),
  glucose: i18n.t("glucose_info"),
  protein: i18n.t("protein_info"),
  ph: i18n.t("ph_info"),
  ketone: i18n.t("ketone_info"),
};

const bloodDetailInfo = {
  normal: i18n.t("blood_good_level_modal"),
  warning: i18n.t("blood_warning_level_modal"),
  caution: i18n.t("blood_caution_level_modal"),
  danger: i18n.t("blood_danger_level_modal"),
};

const glucoseDetailInfo = {
  normal: i18n.t("glucose_good_level_modal"),
  warning: i18n.t("glucose_warning_level_modal"),
  caution: i18n.t("glucose_caution_level_modal"),
  danger: i18n.t("glucose_danger_level_modal"),
};

const proteinDetailInfo = {
  normal: i18n.t("protein_good_level_modal"),
  warning: i18n.t("protein_warning_level_modal"),
  caution: i18n.t("protein_caution_level_modal"),
  danger: i18n.t("protein_danger_level_modal"),
};

const ketonDetailInfo = {
  normal: i18n.t("normal"),
  caution_plus_minus: i18n.t("ketone_caution_level_plus_minus"),
  caution_plus: i18n.t("ketone_caution_level_plus"),
  warning: i18n.t("caution"),
  danger: i18n.t("danger"),
};

const phDetailInfo = {
  normal: i18n.t("ph_good_level_modal"),
  warning: i18n.t("ph_warning_level_modal"),
};

const bloodTxt = {
  normal: i18n.t("blood_good_level"),
  warning: i18n.t("blood_warning_level"),
  caution: i18n.t("blood_caution_level"),
  danger: i18n.t("blood_danger_level"),
};

const glucoseTxt = {
  normal: i18n.t("glucose_good_level"),
  warning: i18n.t("glucose_warning_level"),
  caution: i18n.t("glucose_caution_level"),
  danger: i18n.t("glucose_danger_level"),
};

const proteinTxt = {
  normal: i18n.t("protein_good_level"),
  warning: i18n.t("protein_warning_level"),
  caution: i18n.t("protein_caution_level"),
  danger: i18n.t("protein_danger_level"),
};

const phTxt = {
  normal: i18n.t("ph_normal_level"),
  warning: i18n.t("ph_warning_level"),
};

const ketoneTxt = {
  normal: i18n.t("ketone_normal_level_txt"), // 적절
  caution_plus_minus: i18n.t("ketone_caution_level_plus_minus_txt"), // 주의(+/-)
  caution_plus: i18n.t("ketone_caution_level_plus_txt"), // 주의(+)
  warning: i18n.t("ketone_warning_level_txt"), // 경고
  danger: i18n.t("ketone_danger_level_txt"), // 위험
};

const leukocytesTxt = {
  normal: i18n.t("leukocytes_good_level"), // 적절
  caution: i18n.t("leukocytes_warning_level"), // 주의
  warning: i18n.t("leukocytes_caution_level"), // 경고
  danger: i18n.t("leukocytes_danger_level"), // 위험
};

const bilirubinTxt = {
  normal: i18n.t("bilirubin_good_level"), // 적절
  caution: i18n.t("bilirubin_caution_level"), // 주의
  warning: i18n.t("bilirubin_warning_level"), // 경고
  danger: i18n.t("bilirubin_danger_level"), // 위험
};

const urobilinogenTxt = {
  normal: i18n.t("urobilinogen_good_level"), // 적절
  caution: i18n.t("urobilinogen_caution_level"), // 주의
  warning: i18n.t("urobilinogen_warning_level"), // 경고
  danger: i18n.t("urobilinogen_danger_level"), // 위험
};

const nitriteTxt = {
  normal: i18n.t("nitrite_good_level"), // 적절
  danger: i18n.t("nitrite_danger_level"), // 위험
};

const sgTxt = {
  good: i18n.t("sg_good_level"), // 적절
  normal: i18n.t("sg_normal_level"), // 적절
  caution: i18n.t("sg_caution_level"), // 주의
  warning: i18n.t("sg_warning_level"), // 경고
  danger: i18n.t("sg_danger_level"), // 위험
};

const newObj = {
  blood: bloodTxt,
  leukocytes: leukocytesTxt,
  bilirubin: bilirubinTxt,
  urobilinogen: urobilinogenTxt,
  nitrite: nitriteTxt,
  sg: sgTxt,
};

const cymScoreResultTxt = (score, obj) => {
  console.log(obj);
  switch (score) {
    case "normal":
      return obj.normal;
    case "warning":
      return obj.warning;
    case "caution":
      return obj.caution;
    case "danger":
      return obj.danger;
    default:
      break;
  }
};

const ketoneResultTxt = (score, obj) => {
  /**
   *   normal: i18n.t("ketone_normal_level_txt"), // 적절
  caution_plus_minus: i18n.t("ketone_caution_level_plus_minus_txt"), // 주의(+/-)
  caution_plus: i18n.t("ketone_caution_level_plus_txt"), // 주의(+)
  warning: i18n.t("ketone_warning_level_txt"), // 경고
  danger: i18n.t("ketone_danger_level_txt"), // 위험
   */
  console.log(score);
  switch (score) {
    case "normal":
      return obj.normal;
    case "caution_plus_minus":
      return obj.caution_plus_minus;
    case "caution_plus":
      return obj.caution_plus;
    case "warning":
      return obj.warning;
    case "danger":
      return obj.danger;
    default:
      break;
  }
};

const newCymScoreResultTxt = (score, obj, type) => {
  console.log(obj, score, type);
  if (type === "nitrite") {
    switch (score) {
      case "normal":
        return obj.nitrite.normal;
      case "danger":
        return obj.nitrite.danger;
      default:
        break;
    }

    return i18n.t("nitrite_no_level");
  }

  switch (score) {
    case "good":
      return obj[type].good;
    case "normal":
      return obj[type].normal;
    case "warning":
      return obj[type].warning;
    case "caution":
      return obj[type].caution;
    case "danger":
      return obj[type].danger;
    default:
      console.log("no level", score, type);
      return i18n.t(`${type}_no_level`);
  }
};

function historyTypes(type, score) {
  switch (type) {
    case "blood":
      return cymScoreResultTxt(score, bloodTxt);
    case "glucose":
      return cymScoreResultTxt(score, glucoseTxt);
    case "protein":
      return cymScoreResultTxt(score, proteinTxt);
    case "ph":
      return cymScoreResultTxt(score, phTxt);
    case "ketone":
      return ketoneResultTxt(score, ketoneTxt);
    default:
      return newCymScoreResultTxt(score, newObj, type);
  }
}

export {
  explainations,
  bloodDetailInfo,
  glucoseDetailInfo,
  proteinDetailInfo,
  ketonDetailInfo,
  phDetailInfo,
  bloodTxt,
  glucoseTxt,
  proteinTxt,
  phTxt,
  ketoneTxt,
  historyTypes,
};
